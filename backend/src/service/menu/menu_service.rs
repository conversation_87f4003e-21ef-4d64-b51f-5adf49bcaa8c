use sqlx::{PgPool, Row};
use std::collections::HashMap;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use crate::controller::menu::menu_controller::{
    DetailedMenuResponse, MenuCreateRequest, MenuQueryParams, MenuUpdateRequest, MenuUsageStats,
};

#[derive(Clone)]
pub struct MenuService {
    db_pool: PgPool,
}

impl MenuService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 获取菜单树结构
    pub async fn get_menu_tree(
        &self,
        params: &MenuQueryParams,
    ) -> Result<Vec<DetailedMenuResponse>, String> {
        // 构建查询条件
        let mut where_conditions = vec!["1=1".to_string()];

        if let Some(menu_type) = &params.menu_type {
            where_conditions.push(format!("menu_type = '{}'", menu_type.replace("'", "''")));
        }

        if let Some(parent_id) = &params.parent_id {
            if parent_id.is_empty() {
                where_conditions.push("parent_id IS NULL".to_string());
            } else {
                where_conditions.push(format!("parent_id = '{}'", parent_id.replace("'", "''")));
            }
        }

        if let Some(is_active) = params.is_active {
            where_conditions.push(format!("is_active = {}", is_active));
        }

        if let Some(search) = &params.search {
            where_conditions.push(format!(
                "(name ILIKE '%{}%' OR path ILIKE '%{}%' OR menu_id ILIKE '%{}%')",
                search.replace("'", "''").replace("%", "\\%"),
                search.replace("'", "''").replace("%", "\\%"),
                search.replace("'", "''").replace("%", "\\%")
            ));
        }

        let where_clause = where_conditions.join(" AND ");

        // 构建主查询（移除 required_roles 字段）
        let main_query = format!(
            r#"
            SELECT
                id, menu_id, name, path, icon, parent_id, menu_type,
                description, component_path, external_link,
                access_level, sort_order, is_active, cache_enabled,
                metadata, version, last_modified_by, last_modified_at,
                created_at, updated_at
            FROM public.menu_permissions
            WHERE {}
            ORDER BY
                CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                sort_order ASC,
                name ASC
            "#,
            where_clause
        );

        let rows = sqlx::query(&main_query)
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| {
                error!("Failed to fetch menu tree: {}", e);
                e.to_string()
            })?;

        let mut menus = Vec::new();
        for row in rows {
            let menu_id: String = row.get("menu_id");

            // 获取子菜单数量
            let children_count: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM public.menu_permissions WHERE parent_id = $1",
            )
            .bind(&menu_id)
            .fetch_one(&self.db_pool)
            .await
            .unwrap_or(0);

            // 获取使用统计（如果需要）
            let usage_stats = if params.include_metadata.unwrap_or(false) {
                self.get_menu_usage_stats(&menu_id).await.ok()
            } else {
                None
            };

            // 从 Casbin 策略中获取角色要求
            let required_roles = self.get_menu_required_roles(&menu_id).await.unwrap_or_default();

            let menu = DetailedMenuResponse {
                id: row.get("id"),
                menu_id: menu_id.clone(),
                name: row.get("name"),
                path: row.get("path"),
                icon: row.get("icon"),
                parent_id: row.get("parent_id"),
                menu_type: row.get("menu_type"),
                description: row.get("description"),
                component_path: row.get("component_path"),
                external_link: row.get("external_link"),
                required_roles: if required_roles.is_empty() { None } else { Some(required_roles) },
                access_level: row.get("access_level"),
                sort_order: row.get("sort_order"),
                is_active: row.get("is_active"),
                cache_enabled: row.get("cache_enabled"),
                metadata: row.get("metadata"),
                version: row.get("version"),
                last_modified_by: row.get("last_modified_by"),
                last_modified_at: row
                    .get::<Option<chrono::DateTime<chrono::Utc>>, _>("last_modified_at")
                    .map(|dt| dt.to_rfc3339()),
                created_at: row
                    .get::<chrono::DateTime<chrono::Utc>, _>("created_at")
                    .to_rfc3339(),
                updated_at: row
                    .get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
                    .to_rfc3339(),
                children: None, // 将在后续处理中填充
                children_count: children_count as i32,
                depth_level: 0, // 将在后续处理中计算
                usage_stats,
            };

            menus.push(menu);
        }

        // 如果需要包含子菜单，构建树形结构
        if params.include_children.unwrap_or(true) {
            menus = self.build_menu_tree(menus);
        }

        Ok(menus)
    }

    /// 创建新菜单
    pub async fn create_menu(
        &self,
        request: &MenuCreateRequest,
        user_id: Uuid,
    ) -> Result<DetailedMenuResponse, String> {
        // 验证菜单ID唯一性
        let existing_count: i64 =
            sqlx::query_scalar("SELECT COUNT(*) FROM public.menu_permissions WHERE menu_id = $1")
                .bind(&request.menu_id)
                .fetch_one(&self.db_pool)
                .await
                .map_err(|e| {
                    error!("Failed to check menu ID uniqueness: {}", e);
                    e.to_string()
                })?;

        if existing_count > 0 {
            warn!(
                "Attempt to create menu with duplicate ID: {}",
                request.menu_id
            );
            return Err("MENU_ID_EXISTS".to_string());
        }

        // 验证父菜单存在性（如果指定了父菜单）
        if let Some(parent_id) = &request.parent_id {
            let parent_exists: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM public.menu_permissions WHERE menu_id = $1",
            )
            .bind(parent_id)
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| {
                error!("Failed to check parent menu existence: {}", e);
                e.to_string()
            })?;

            if parent_exists == 0 {
                warn!(
                    "Attempt to create menu with non-existent parent: {}",
                    parent_id
                );
                return Err("PARENT_NOT_FOUND".to_string());
            }
        }

        // 开始事务
        let mut tx = self.db_pool.begin().await.map_err(|e| {
            error!("Failed to start transaction: {}", e);
            e.to_string()
        })?;

        // 插入新菜单（移除 required_roles 字段）
        let _new_menu_id: Uuid = sqlx::query_scalar::<_, Uuid>(
            r#"
            INSERT INTO public.menu_permissions (
                menu_id, name, path, icon, parent_id, menu_type,
                description, component_path, external_link,
                access_level, sort_order, is_active,
                cache_enabled, metadata, last_modified_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
            ) RETURNING id
            "#,
        )
        .bind(&request.menu_id)
        .bind(&request.name)
        .bind(&request.path)
        .bind(&request.icon)
        .bind(&request.parent_id)
        .bind(request.menu_type.as_deref().unwrap_or("functional"))
        .bind(&request.description)
        .bind(&request.component_path)
        .bind(&request.external_link)
        .bind(request.access_level.unwrap_or(0))
        .bind(request.sort_order.unwrap_or(0))
        .bind(request.is_active.unwrap_or(true))
        .bind(true) // cache_enabled 默认为 true
        .bind(&request.metadata)
        .bind(user_id)
        .fetch_one(&mut *tx)
        .await
        .map_err(|e| {
            error!("Failed to create menu: {}", e);
            e.to_string()
        })?;

        // 创建菜单的Casbin策略（包括角色权限）
        self.create_menu_casbin_policies(&mut tx, request).await?;

        // 提交事务
        tx.commit().await.map_err(|e| {
            error!("Failed to commit transaction: {}", e);
            e.to_string()
        })?;

        // 获取新创建的菜单详情
        self.get_menu_by_id(&request.menu_id).await
    }

    /// 为菜单创建Casbin策略
    async fn create_menu_casbin_policies(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        request: &MenuCreateRequest,
    ) -> Result<(), String> {
        // 1. 为每个所需角色创建菜单访问策略
        // 格式: p, role:class_teacher, *, menu:teaching-aids-management, access, allow
        if let Some(required_roles) = &request.required_roles {
            if !required_roles.is_empty() {
                for role in required_roles {
                    let policy_exists = sqlx::query_scalar!(
                        r#"
                        SELECT COUNT(*) FROM public.casbin_policies
                        WHERE ptype = $1 AND v0 = $2 AND v1 = $3 AND v2 = $4 AND v3 = $5
                        "#,
                        "p",
                        format!("role:{}", role),
                        "*",
                        format!("menu:{}", request.menu_id),
                        "access"
                    )
                    .fetch_one(&mut **tx)
                    .await
                    .map_err(|e| e.to_string())?
                    .unwrap_or(0) > 0;

                    if !policy_exists {
                        sqlx::query!(
                            r#"
                            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
                            VALUES ($1, $2, $3, $4, $5, $6, $7)
                            "#,
                            "p",
                            format!("role:{}", role), // v0: 角色标识
                            "*",         // v1: 域（通配符，适用所有租户）
                            format!("menu:{}", request.menu_id), // v2: 菜单对象
                            "access",    // v3: 访问动作
                            "allow",     // v4: 允许效果
                            "access"     // v5: 简化元数据
                        )
                        .execute(&mut **tx)
                        .await
                        .map_err(|e| {
                            error!("Failed to create menu access policy for role {}: {}", role, e);
                            e.to_string()
                        })?;
                    }
                }
            }
        }

        // 2. 不再需要单独的权限要求策略和数据范围策略，所有权限控制通过角色实现

        Ok(())
    }

    /// 更新菜单
    pub async fn update_menu(
        &self,
        menu_id: &str,
        request: &MenuUpdateRequest,
        user_id: Uuid,
    ) -> Result<DetailedMenuResponse, String> {
        // 检查菜单是否存在
        let menu_exists: i64 =
            sqlx::query_scalar("SELECT COUNT(*) FROM public.menu_permissions WHERE menu_id = $1")
                .bind(menu_id)
                .fetch_one(&self.db_pool)
                .await
                .map_err(|e| {
                    error!("Failed to check menu existence: {}", e);
                    e.to_string()
                })?;

        if menu_exists == 0 {
            return Err("MENU_NOT_FOUND".to_string());
        }

        // 开始事务
        let mut tx = self.db_pool.begin().await.map_err(|e| {
            error!("Failed to start transaction: {}", e);
            e.to_string()
        })?;

        // 更新菜单基础信息
        let affected_rows = sqlx::query!(
            r#"
            UPDATE public.menu_permissions
            SET
                name = COALESCE($1, name),
                path = COALESCE($2, path),
                icon = COALESCE($3, icon),
                description = COALESCE($4, description),
                is_active = COALESCE($5, is_active),
                metadata = COALESCE($6, metadata),
                last_modified_by = $7,
                last_modified_at = NOW()
            WHERE menu_id = $8
            "#,
            request.name,
            request.path,
            request.icon,
            request.description,
            request.is_active,
            request.metadata,
            user_id,
            menu_id
        )
        .execute(&mut *tx)
        .await
        .map_err(|e| {
            error!("Failed to update menu: {}", e);
            e.to_string()
        })?;

        if affected_rows.rows_affected() == 0 {
            return Err("MENU_NOT_FOUND".to_string());
        }

        // 更新菜单的Casbin策略
        self.update_menu_casbin_policies(&mut tx, menu_id, request)
            .await?;

        // 提交事务
        tx.commit().await.map_err(|e| {
            error!("Failed to commit transaction: {}", e);
            e.to_string()
        })?;

        // 获取更新后的菜单详情
        self.get_menu_by_id(menu_id).await
    }

    /// 更新菜单的Casbin策略
    async fn update_menu_casbin_policies(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        menu_id: &str,
        request: &MenuUpdateRequest,
    ) -> Result<(), String> {
        // 1. 更新菜单访问策略（如果提供了新的角色要求）
        if let Some(required_roles) = &request.required_roles {
            // 先删除现有的菜单访问策略

            sqlx::query!(
                r#"
                DELETE FROM public.casbin_policies
                WHERE v2 = $1
                "#,
                format!("menu:{}", menu_id),
            )
            .execute(&mut **tx)
            .await
            .map_err(|e| e.to_string())?;

            // 如果有角色要求，为每个角色创建菜单访问策略
            // 格式: p, role:class_teacher, *, menu:teaching-aids-management, access, allow
            if !required_roles.is_empty() {
                for role in required_roles {
                    sqlx::query!(
                        r#"
                        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
                        VALUES ($1, $2, $3, $4, $5, $6, $7)
                        "#,
                        "p",
                        format!("role:{}", role),    // v0: 角色标识
                        "tenant_*",                         // v1: 域（通配符）
                        format!("menu:{}", menu_id), // v2: 菜单对象
                        "access",                    // v3: 访问动作
                        "allow",                     // v4: 允许效果
                        "access"                     // v5: 简化元数据
                    )
                    .execute(&mut **tx)
                    .await
                    .map_err(|e| {
                        error!(
                            "Failed to create menu access policy for role {}: {}",
                            role, e
                        );
                        e.to_string()
                    })?;
                }
            }
        }

        Ok(())
    }

    /// 删除菜单
    pub async fn delete_menu(&self, menu_id: &str, force_delete: bool) -> Result<(), String> {
        // 检查是否有子菜单
        let children_count: i64 =
            sqlx::query_scalar("SELECT COUNT(*) FROM public.menu_permissions WHERE parent_id = $1")
                .bind(menu_id)
                .fetch_one(&self.db_pool)
                .await
                .map_err(|e| {
                    error!("Failed to check menu children: {}", e);
                    e.to_string()
                })?;

        // 如果有子菜单且不是强制删除，返回错误
        if children_count > 0 && !force_delete {
            warn!(
                "Attempt to delete menu {} with {} children without force flag",
                menu_id, children_count
            );
            return Err("HAS_CHILDREN".to_string());
        }

        // 开始事务
        let mut tx = self.db_pool.begin().await.map_err(|e| {
            error!("Failed to begin transaction: {}", e);
            e.to_string()
        })?;

        // 如果是强制删除，先删除所有子菜单
        if force_delete && children_count > 0 {
            sqlx::query("DELETE FROM public.menu_permissions WHERE parent_id = $1")
                .bind(menu_id)
                .execute(&mut *tx)
                .await
                .map_err(|e| {
                    error!("Failed to delete child menus: {}", e);
                    e.to_string()
                })?;
        }

        // 删除菜单相关的Casbin策略
        self.delete_menu_casbin_policies(&mut tx, menu_id, force_delete)
            .await?;

        // 删除主菜单
        let affected_rows = sqlx::query("DELETE FROM public.menu_permissions WHERE menu_id = $1")
            .bind(menu_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| {
                error!("Failed to delete menu: {}", e);
                e.to_string()
            })?;

        if affected_rows.rows_affected() == 0 {
            return Err("MENU_NOT_FOUND".to_string());
        }

        // 提交事务
        tx.commit().await.map_err(|e| {
            error!("Failed to commit transaction: {}", e);
            e.to_string()
        })?;

        Ok(())
    }

    /// 删除菜单的Casbin策略
    async fn delete_menu_casbin_policies(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        menu_id: &str,
        force_delete: bool,
    ) -> Result<(), String> {
        // 如果是强制删除，需要删除所有子菜单的策略
        if force_delete {
            // 获取所有子菜单ID
            let child_menu_ids: Vec<String> = sqlx::query_scalar(
                "SELECT menu_id FROM public.menu_permissions WHERE parent_id = $1",
            )
            .bind(menu_id)
            .fetch_all(&mut **tx)
            .await
            .map_err(|e| {
                error!("Failed to get child menu IDs: {}", e);
                e.to_string()
            })?;

            // 删除所有子菜单的策略
            for child_menu_id in child_menu_ids {
                sqlx::query!(
                    "DELETE FROM public.casbin_policies WHERE v2 LIKE $1",
                    format!("menu:{}%", child_menu_id)
                )
                .execute(&mut **tx)
                .await
                .map_err(|e| {
                    error!("Failed to delete child menu policies: {}", e);
                    e.to_string()
                })?;
            }
        }

        // 删除主菜单的所有策略
        sqlx::query!(
            "DELETE FROM public.casbin_policies WHERE v2 LIKE $1",
            format!("menu:{}%", menu_id)
        )
        .execute(&mut **tx)
        .await
        .map_err(|e| {
            error!("Failed to delete menu policies: {}", e);
            e.to_string()
        })?;

        Ok(())
    }

    /// 从 Casbin 策略中获取菜单的角色要求
    pub async fn get_menu_required_roles(&self, menu_id: &str) -> Result<Vec<String>, String> {
        let policies = sqlx::query!(
            r#"
            SELECT v0 as role_subject
            FROM public.casbin_policies
            WHERE ptype = 'p'
            AND v2 = $1
            AND v3 = 'access'
            AND v4 = 'allow'
            AND v0 LIKE 'role:%'
            "#,
            format!("menu:{}", menu_id)
        )
        .fetch_all(&self.db_pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch menu required roles: {}", e);
            e.to_string()
        })?;

        let mut roles = Vec::new();
        for policy in policies {
            if let Some(role_subject) = policy.role_subject {
                // 从 "role:class_teacher" 中提取 "class_teacher"
                if let Some(role_code) = role_subject.strip_prefix("role:") {
                    roles.push(role_code.to_string());
                }
            }
        }

        Ok(roles)
    }

    /// 获取菜单详情
    pub async fn get_menu_by_id(&self, menu_id: &str) -> Result<DetailedMenuResponse, String> {
        let row = sqlx::query(
            r#"
            SELECT
                id, menu_id, name, path, icon, parent_id, menu_type,
                description, component_path, external_link,
                access_level, sort_order, is_active, cache_enabled,
                metadata, version, last_modified_by, last_modified_at,
                created_at, updated_at
            FROM public.menu_permissions
            WHERE menu_id = $1
            "#,
        )
        .bind(menu_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch menu by ID: {}", e);
            e.to_string()
        })?;

        match row {
            Some(row) => {
                // 获取子菜单数量
                let children_count: i64 = sqlx::query_scalar(
                    "SELECT COUNT(*) FROM public.menu_permissions WHERE parent_id = $1",
                )
                .bind(menu_id)
                .fetch_one(&self.db_pool)
                .await
                .unwrap_or(0);

                // 从 Casbin 策略中获取角色要求
                let required_roles = self.get_menu_required_roles(menu_id).await?;

                Ok(DetailedMenuResponse {
                    id: row.get("id"),
                    menu_id: row.get("menu_id"),
                    name: row.get("name"),
                    path: row.get("path"),
                    icon: row.get("icon"),
                    parent_id: row.get("parent_id"),
                    menu_type: row.get("menu_type"),
                    description: row.get("description"),
                    component_path: row.get("component_path"),
                    external_link: row.get("external_link"),
                    required_roles: if required_roles.is_empty() { None } else { Some(required_roles) },
                    access_level: row.get("access_level"),
                    sort_order: row.get("sort_order"),
                    is_active: row.get("is_active"),
                    cache_enabled: row.get("cache_enabled"),
                    metadata: row.get("metadata"),
                    version: row.get("version"),
                    last_modified_by: row.get("last_modified_by"),
                    last_modified_at: row
                        .get::<Option<chrono::DateTime<chrono::Utc>>, _>("last_modified_at")
                        .map(|dt| dt.to_rfc3339()),
                    created_at: row
                        .get::<chrono::DateTime<chrono::Utc>, _>("created_at")
                        .to_rfc3339(),
                    updated_at: row
                        .get::<chrono::DateTime<chrono::Utc>, _>("updated_at")
                        .to_rfc3339(),
                    children: None,
                    children_count: children_count as i32,
                    depth_level: 0,
                    usage_stats: None,
                })
            }
            None => Err("MENU_NOT_FOUND".to_string()),
        }
    }

    /// 获取菜单使用统计
    pub async fn get_menu_usage_stats(&self, menu_id: &str) -> Result<MenuUsageStats, String> {
        // 检查表是否存在，如果不存在则返回默认统计
        let table_exists = sqlx::query_scalar::<_, bool>(
            r#"
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'menu_usage_statistics'
            )
            "#,
        )
        .fetch_one(&self.db_pool)
        .await
        .unwrap_or(false);

        if !table_exists {
            debug!("menu_usage_statistics table does not exist, returning default stats");
            return Ok(MenuUsageStats {
                total_access_count: 0,
                unique_user_count: 0,
                denied_access_count: 0,
                last_accessed_at: None,
                avg_daily_access: 0.0,
            });
        }

        let row = sqlx::query(
            r#"
            SELECT
                COALESCE(SUM(access_count), 0) as total_access_count,
                COALESCE(SUM(unique_user_count), 0) as unique_user_count,
                COALESCE(SUM(denied_access_count), 0) as denied_access_count,
                MAX(last_accessed_at) as last_accessed_at,
                COALESCE(AVG(access_count)::FLOAT8, 0.0) as avg_daily_access
            FROM public.menu_usage_statistics
            WHERE menu_id = $1
            "#,
        )
        .bind(menu_id)
        .fetch_optional(&self.db_pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch menu usage stats: {}", e);
            e.to_string()
        })?;

        match row {
            Some(row) => Ok(MenuUsageStats {
                total_access_count: row.get::<i64, _>("total_access_count"),
                unique_user_count: row.get::<i64, _>("unique_user_count"),
                denied_access_count: row.get::<i64, _>("denied_access_count"),
                last_accessed_at: row
                    .get::<Option<chrono::DateTime<chrono::Utc>>, _>("last_accessed_at")
                    .map(|dt| dt.to_rfc3339()),
                avg_daily_access: row.get::<f64, _>("avg_daily_access"),
            }),
            None => Ok(MenuUsageStats {
                total_access_count: 0,
                unique_user_count: 0,
                denied_access_count: 0,
                last_accessed_at: None,
                avg_daily_access: 0.0,
            }),
        }
    }

    /// 构建菜单树结构
    pub fn build_menu_tree(&self, menus: Vec<DetailedMenuResponse>) -> Vec<DetailedMenuResponse> {
        let mut menu_map = HashMap::new();
        let mut root_menus = Vec::new();

        // 将菜单按ID索引
        for menu in menus {
            menu_map.insert(menu.menu_id.clone(), menu);
        }

        // 构建树形结构
        let menu_ids: Vec<String> = menu_map.keys().cloned().collect();

        for menu_id in menu_ids {
            if let Some(menu) = menu_map.remove(&menu_id) {
                if let Some(parent_id) = &menu.parent_id {
                    // 有父菜单，添加到父菜单的children中
                    if let Some(parent) = menu_map.get_mut(parent_id) {
                        if parent.children.is_none() {
                            parent.children = Some(Vec::new());
                        }
                        parent.children.as_mut().unwrap().push(menu);
                    } else {
                        // 父菜单不存在，作为根菜单处理
                        root_menus.push(menu);
                    }
                } else {
                    // 根菜单
                    root_menus.push(menu);
                }
            }
        }

        // 将剩余的菜单（父菜单）添加到根菜单列表
        for (_, menu) in menu_map {
            root_menus.push(menu);
        }

        // 对菜单进行排序
        root_menus.sort_by(|a, b| a.sort_order.cmp(&b.sort_order));

        // 递归排序子菜单
        for menu in &mut root_menus {
            self.sort_menu_children(menu);
        }

        root_menus
    }

    /// 递归排序菜单子项
    fn sort_menu_children(&self, menu: &mut DetailedMenuResponse) {
        if let Some(children) = &mut menu.children {
            children.sort_by(|a, b| a.sort_order.cmp(&b.sort_order));

            for child in children {
                self.sort_menu_children(child);
            }
        }
    }
}
