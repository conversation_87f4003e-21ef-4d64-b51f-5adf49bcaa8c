use anyhow::Result;
use sqlx::{Pg<PERSON><PERSON>, QueryBuilder, Postgres};
use uuid::Uuid;
use tracing::{info, error};
use std::sync::Arc;

use super::{
    IdentityBasedDataFilter,
    DataFilterManager,
    FilterContext,
    CasbinPermissionService,
};

/// 集成基于身份的数据过滤器的示例
/// 展示如何在学生查询服务中使用新的过滤机制
pub struct StudentQueryServiceWithIdentityFilter {
    pool: PgPool,
    data_filter_manager: Arc<DataFilterManager>,
    casbin_service: Arc<dyn CasbinPermissionService>,
}

impl StudentQueryServiceWithIdentityFilter {
    pub fn new(
        pool: PgPool,
        casbin_service: Arc<dyn CasbinPermissionService>,
    ) -> Self {
        // 创建数据过滤器管理器
        let mut data_filter_manager = DataFilterManager::new();
        
        // 注册基于身份的数据过滤器
        let identity_filter = Arc::new(IdentityBasedDataFilter::new(pool.clone()));
        data_filter_manager.register_filter("student".to_string(), identity_filter);
        
        Self {
            pool,
            data_filter_manager: Arc::new(data_filter_manager),
            casbin_service,
        }
    }

    /// 查询学生列表（使用基于身份的数据过滤）
    pub async fn query_students_with_identity_filter(
        &self,
        user_id: Uuid,
        tenant_id: String,
        schema_name: String,
        page: Option<i64>,
        page_size: Option<i64>,
        // 其他查询参数...
    ) -> Result<(Vec<StudentInfo>, i64)> {
        let page = page.unwrap_or(1);
        let page_size = page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        // 构建基础查询
        let mut query_builder = QueryBuilder::new(
            r#"
            SELECT 
                s.id,
                s.student_id,
                s.name,
                s.administrative_class_id,
                ac.name as class_name,
                s.created_at
            FROM students s
            LEFT JOIN administrative_classes ac ON s.administrative_class_id = ac.id
            WHERE 1=1
            "#
        );

        let mut count_builder = QueryBuilder::new(
            "SELECT COUNT(*) FROM students s WHERE 1=1"
        );

        // 创建过滤上下文
        let filter_context = FilterContext {
            user_id,
            tenant_id: tenant_id.clone(),
            user_identity: format!("user:{}", user_id),
            resource: "student".to_string(),
            action: "read".to_string(),
            schema_name: schema_name.clone(),
        };

        // 应用基于身份的数据过滤
        match self.data_filter_manager.apply_data_filter(
            &filter_context,
            &mut query_builder,
            &mut count_builder,
            self.casbin_service.as_ref(),
        ).await {
            Ok(filter_applied) => {
                info!("Identity-based data filter applied: {}, for user {} in tenant {}",
                      filter_applied, user_id, tenant_id);
            },
            Err(e) => {
                error!("Failed to apply identity-based data filter: {}", e);
                // 如果过滤器失败，返回空结果以确保安全
                return Ok((Vec::new(), 0));
            }
        }

        // 添加分页
        query_builder.push(" ORDER BY s.created_at DESC LIMIT ");
        query_builder.push_bind(page_size);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(offset);

        // 执行查询
        let students = query_builder
            .build_query_as::<StudentInfo>()
            .fetch_all(&self.pool)
            .await?;

        let total_count: (i64,) = count_builder
            .build_query_as()
            .fetch_one(&self.pool)
            .await?;

        Ok((students, total_count.0))
    }

    /// 演示不同身份用户的数据访问范围
    pub async fn demonstrate_identity_based_access(&self) -> Result<()> {
        info!("=== 基于身份的数据访问范围演示 ===");

        // 模拟不同身份的用户
        let test_cases = vec![
            (
                Uuid::new_v4(), // 校长用户ID
                "principal",
                "可以访问整个学校的所有学生数据"
            ),
            (
                Uuid::new_v4(), // 年级长用户ID
                "grade_director", 
                "只能访问所负责年级的学生数据"
            ),
            (
                Uuid::new_v4(), // 班主任用户ID
                "class_teacher",
                "只能访问所负责班级的学生数据"
            ),
            (
                Uuid::new_v4(), // 任课老师用户ID
                "subject_teacher",
                "只能访问所教授班级的学生数据"
            ),
            (
                Uuid::new_v4(), // 学生用户ID
                "student",
                "只能访问自己的数据"
            ),
            (
                Uuid::new_v4(), // 家长用户ID
                "parent",
                "只能访问自己孩子的数据"
            ),
        ];

        for (user_id, role, description) in test_cases {
            info!("测试用户身份: {} - {}", role, description);
            
            let result = self.query_students_with_identity_filter(
                user_id,
                "tenant_001".to_string(),
                "tenant_001".to_string(),
                Some(1),
                Some(10),
            ).await;

            match result {
                Ok((students, total)) => {
                    info!("  -> 查询结果: {} 条记录，总计 {} 条", students.len(), total);
                },
                Err(e) => {
                    error!("  -> 查询失败: {}", e);
                }
            }
        }

        Ok(())
    }
}

/// 学生信息结构体（示例）
#[derive(Debug, sqlx::FromRow)]
pub struct StudentInfo {
    pub id: Uuid,
    pub student_id: String,
    pub name: String,
    pub administrative_class_id: Option<Uuid>,
    pub class_name: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 配置基于身份的数据过滤器的工厂函数
pub fn setup_identity_based_data_filters(
    pool: PgPool,
) -> DataFilterManager {
    let mut manager = DataFilterManager::new();
    
    // 注册学生数据过滤器
    let student_filter = Arc::new(IdentityBasedDataFilter::new(pool.clone()));
    manager.register_filter("student".to_string(), student_filter);
    
    // 可以为其他资源类型注册相应的过滤器
    let class_filter = Arc::new(IdentityBasedDataFilter::new(pool.clone()));
    manager.register_filter("administrative_class".to_string(), class_filter);
    
    let teacher_filter = Arc::new(IdentityBasedDataFilter::new(pool.clone()));
    manager.register_filter("teacher".to_string(), teacher_filter);
    
    manager
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::service::permission::MultiTenantCasbinService;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_identity_based_filtering() {
        // 这里可以添加集成测试
        // 测试不同身份用户的数据访问权限
    }

    #[tokio::test]
    async fn test_filter_condition_generation() {
        // 测试过滤条件的生成逻辑
    }

    #[tokio::test]
    async fn test_multiple_identities_user() {
        // 测试拥有多个身份的用户的数据访问权限
    }
}

/// 使用说明和最佳实践
/// 
/// 1. **权限策略配置**：
///    在casbin_policies表中只需要配置通配符权限，例如：
///    - ('p', 'principal', 'tenant_001', 'student:*', 'read', 'allow')
///    - ('p', 'class_teacher', 'tenant_001', 'student:*', 'read', 'allow')
/// 
/// 2. **身份信息维护**：
///    确保user_identities表中正确记录了用户的身份信息，包括：
///    - role_id: 角色ID
///    - target_type: 目标类型（school, grade, class, student等）
///    - target_id: 具体的目标ID（年级ID、班级ID等）
/// 
/// 3. **扩展新的身份类型**：
///    在IdentityBasedDataFilter的generate_scope_conditions_by_identity方法中
///    添加新的匹配模式来支持新的身份类型
/// 
/// 4. **性能优化**：
///    - 考虑缓存用户身份信息
///    - 对于复杂的关联查询，可以预计算数据范围
///    - 使用数据库索引优化过滤条件的执行
/// 
/// 5. **安全考虑**：
///    - 默认拒绝访问（当无法确定权限时）
///    - 记录权限检查的审计日志
///    - 定期验证权限配置的正确性
