use anyhow::{Result, anyhow};
use async_trait::async_trait;
use sqlx::{PgPool, QueryBuilder, Postgres, FromRow};
use uuid::Uuid;
use tracing::{info, debug, warn, error};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

use super::{CasbinPermissionService, DataFilter, FilterContext, FilterCondition, FilterParam};

/// 增强的Casbin策略记录
/// 基于现有的casbin_policies表结构
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct EnhancedCasbinPolicy {
    pub id: i64,
    pub ptype: String,
    pub v0: Option<String>,    // subject (角色名)
    pub v1: Option<String>,    // domain (租户ID)
    pub v2: Option<String>,    // object (资源:范围)
    pub v3: Option<String>,    // action (操作类型)
    pub v4: Option<String>,    // effect (allow/deny)
    pub v5: Option<String>,    // 扩展字段：存储SQL条件模板或其他元数据
    pub tenant_id: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// 基于现有casbin_policies表的增强数据过滤器
/// 利用v5字段存储SQL条件模板，实现优雅的策略驱动过滤
#[derive(Debug, Clone)]
pub struct EnhancedCasbinDataFilter {
    pool: PgPool,
    template_cache: std::sync::Arc<std::sync::RwLock<HashMap<String, String>>>,
}

impl EnhancedCasbinDataFilter {
    pub fn new(pool: PgPool) -> Self {
        Self {
            pool,
            template_cache: std::sync::Arc::new(std::sync::RwLock::new(HashMap::new())),
        }
    }

    /// 从现有casbin_policies表获取数据过滤条件
    async fn get_filter_from_casbin_policies(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        info!("EnhancedCasbinDataFilter: Getting filter from casbin_policies for user: {}", context.user_id);

        // 1. 获取用户角色
        let user_roles = self.get_user_roles(context).await?;
        if user_roles.is_empty() {
            warn!("No roles found for user: {}", context.user_id);
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无角色，拒绝访问
                bind_params: vec![],
            }));
        }

        debug!("User roles: {:?}", user_roles);

        // 2. 查询匹配的策略，优先查找有SQL模板的策略
        let policies = self.query_enhanced_policies(
            &user_roles,
            &context.tenant_id,
            &context.resource,
            &context.action,
        ).await?;

        if policies.is_empty() {
            warn!("No matching policies found for user: {}, resource: {}", 
                  context.user_id, context.resource);
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无策略，拒绝访问
                bind_params: vec![],
            }));
        }

        // 3. 按优先级选择策略：有SQL模板的策略优先
        let selected_policy = self.select_best_policy(&policies)?;

        info!("Selected policy: {} -> {} (v5: {:?})", 
              selected_policy.v0.as_deref().unwrap_or("unknown"),
              selected_policy.v2.as_deref().unwrap_or("unknown"),
              selected_policy.v5);

        // 4. 生成过滤条件
        self.generate_filter_condition(&selected_policy, context).await
    }

    /// 获取用户角色
    async fn get_user_roles(&self, context: &FilterContext) -> Result<Vec<String>> {
        let query = format!(
            r#"
            SELECT DISTINCT r.name as role_name
            FROM "{schema}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            "#,
            schema = context.schema_name
        );

        let roles: Vec<String> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(roles)
    }

    /// 查询增强的策略
    async fn query_enhanced_policies(
        &self,
        user_roles: &[String],
        tenant_id: &str,
        resource: &str,
        action: &str,
    ) -> Result<Vec<EnhancedCasbinPolicy>> {
        if user_roles.is_empty() {
            return Ok(vec![]);
        }

        // 构建角色查询条件
        let role_conditions: Vec<String> = user_roles.iter()
            .map(|role| format!("v0 = 'role:{}'", role))
            .collect();
        let role_condition = role_conditions.join(" OR ");

        // 构建资源匹配条件
        let resource_patterns = vec![
            format!("v2 = '{}:*'", resource),           // 通配符匹配
            format!("v2 LIKE '{}:%'", resource),        // 前缀匹配
            "v2 = '*'".to_string(),                     // 全局匹配
        ];
        let resource_condition = resource_patterns.join(" OR ");

        let query = format!(
            r#"
            SELECT * FROM public.casbin_policies
            WHERE ptype = 'p'
            AND ({})
            AND (v1 = $1 OR v1 = '*')
            AND ({})
            AND (v3 = $2 OR v3 = '*')
            AND v4 = 'allow'
            ORDER BY 
                CASE WHEN v5 IS NOT NULL AND v5 != '' THEN 1 ELSE 2 END,  -- 有SQL模板的优先
                CASE WHEN v2 = '{}:*' THEN 1 
                     WHEN v2 LIKE '{}:%' THEN 2 
                     ELSE 3 END,  -- 精确匹配优先
                created_at DESC
            "#,
            role_condition, resource_condition, resource, resource
        );

        let policies = sqlx::query_as::<_, EnhancedCasbinPolicy>(&query)
            .bind(tenant_id)
            .bind(action)
            .fetch_all(&self.pool)
            .await?;

        debug!("Found {} matching policies", policies.len());
        Ok(policies)
    }

    /// 选择最佳策略
    fn select_best_policy(&self, policies: &[EnhancedCasbinPolicy]) -> Result<&EnhancedCasbinPolicy> {
        // 策略已经按优先级排序，选择第一个
        policies.first()
            .ok_or_else(|| anyhow!("No policies available"))
    }

    /// 生成过滤条件
    async fn generate_filter_condition(
        &self,
        policy: &EnhancedCasbinPolicy,
        context: &FilterContext,
    ) -> Result<Option<FilterCondition>> {
        // 1. 如果v5字段包含SQL模板，直接使用
        if let Some(v5) = &policy.v5 {
            if self.is_sql_template(v5) {
                let sql_condition = self.render_sql_template(v5, context).await?;
                return Ok(Some(FilterCondition {
                    sql_condition,
                    bind_params: vec![],
                }));
            }
        }

        // 2. 如果没有SQL模板，根据v2对象字段解析生成条件
        if let Some(object) = &policy.v2 {
            return self.generate_condition_from_object(object, context).await;
        }

        // 3. 默认拒绝访问
        Ok(Some(FilterCondition {
            sql_condition: "1=0".to_string(),
            bind_params: vec![],
        }))
    }

    /// 判断是否为SQL模板
    fn is_sql_template(&self, v5: &str) -> bool {
        // 简单判断：包含SQL关键字或变量占位符
        let sql_indicators = vec!["SELECT", "WHERE", "IN", "=", "${", "s.", "ac."];
        let upper_v5 = v5.to_uppercase();
        
        sql_indicators.iter().any(|indicator| upper_v5.contains(indicator))
    }

    /// 渲染SQL模板
    async fn render_sql_template(
        &self,
        template: &str,
        context: &FilterContext,
    ) -> Result<String> {
        let mut rendered = template.to_string();

        // 基本变量替换
        rendered = rendered.replace("${user_id}", &format!("'{}'", context.user_id));
        rendered = rendered.replace("${tenant_id}", &format!("'{}'", context.tenant_id));
        rendered = rendered.replace("${schema_name}", &format!("\"{}\"", context.schema_name));

        // 高级变量替换
        rendered = self.process_advanced_template_variables(rendered, context).await?;

        debug!("Rendered SQL template: {}", rendered);
        Ok(rendered)
    }

    /// 处理高级模板变量
    async fn process_advanced_template_variables(
        &self,
        mut template: String,
        context: &FilterContext,
    ) -> Result<String> {
        // 处理用户管理的班级列表
        if template.contains("${user.managed_classes}") {
            let managed_classes = self.get_user_managed_classes(context).await?;
            let classes_str = if managed_classes.is_empty() {
                "NULL".to_string()
            } else {
                managed_classes.iter()
                    .map(|id| format!("'{}'", id))
                    .collect::<Vec<_>>()
                    .join(",")
            };
            template = template.replace("${user.managed_classes}", &classes_str);
        }

        // 处理用户管理的年级列表
        if template.contains("${user.managed_grades}") {
            let managed_grades = self.get_user_managed_grades(context).await?;
            let grades_str = if managed_grades.is_empty() {
                "NULL".to_string()
            } else {
                managed_grades.iter()
                    .map(|id| format!("'{}'", id))
                    .collect::<Vec<_>>()
                    .join(",")
            };
            template = template.replace("${user.managed_grades}", &grades_str);
        }

        // 处理用户对应的学生ID
        if template.contains("${user.student_id}") {
            if let Some(student_id) = self.get_user_student_id(context).await? {
                template = template.replace("${user.student_id}", &format!("'{}'", student_id));
            } else {
                template = template.replace("${user.student_id}", "NULL");
            }
        }

        Ok(template)
    }

    /// 根据对象字段生成条件
    async fn generate_condition_from_object(
        &self,
        object: &str,
        context: &FilterContext,
    ) -> Result<Option<FilterCondition>> {
        let obj_parts: Vec<&str> = object.split(':').collect();
        
        if obj_parts.len() < 2 {
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(),
                bind_params: vec![],
            }));
        }

        let resource = obj_parts[0];
        let scope_type = obj_parts[1];

        if resource != &context.resource {
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(),
                bind_params: vec![],
            }));
        }

        match scope_type {
            "*" | "school" => {
                // 全校权限
                Ok(Some(FilterCondition {
                    sql_condition: "1=1".to_string(),
                    bind_params: vec![],
                }))
            },
            "class" => {
                // 班级权限
                let managed_classes = self.get_user_managed_classes(context).await?;
                if managed_classes.is_empty() {
                    return Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }));
                }

                let condition = format!(
                    "s.administrative_class_id IN ({})",
                    managed_classes.iter().map(|_| "?").collect::<Vec<_>>().join(",")
                );

                let params = managed_classes.into_iter()
                    .map(FilterParam::Uuid)
                    .collect();

                Ok(Some(FilterCondition {
                    sql_condition: condition,
                    bind_params: params,
                }))
            },
            "grade" => {
                // 年级权限
                let managed_grades = self.get_user_managed_grades(context).await?;
                if managed_grades.is_empty() {
                    return Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }));
                }

                let condition = format!(
                    "s.grade_id IN ({})",
                    managed_grades.iter().map(|_| "?").collect::<Vec<_>>().join(",")
                );

                let params = managed_grades.into_iter()
                    .map(FilterParam::Uuid)
                    .collect();

                Ok(Some(FilterCondition {
                    sql_condition: condition,
                    bind_params: params,
                }))
            },
            "self" => {
                // 自己的数据
                if let Some(student_id) = self.get_user_student_id(context).await? {
                    Ok(Some(FilterCondition {
                        sql_condition: "s.id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(student_id)],
                    }))
                } else {
                    Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }))
                }
            },
            _ => {
                warn!("Unknown scope type: {}", scope_type);
                Ok(Some(FilterCondition {
                    sql_condition: "1=0".to_string(),
                    bind_params: vec![],
                }))
            }
        }
    }

    /// 获取用户管理的班级ID列表
    async fn get_user_managed_classes(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'class' AND target_id IS NOT NULL
            "#,
            schema = context.schema_name
        );

        let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(class_ids)
    }

    /// 获取用户管理的年级ID列表
    async fn get_user_managed_grades(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'grade' AND target_id IS NOT NULL
            "#,
            schema = context.schema_name
        );

        let grade_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(grade_ids)
    }

    /// 获取用户对应的学生ID
    async fn get_user_student_id(&self, context: &FilterContext) -> Result<Option<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'student' AND target_id IS NOT NULL
            LIMIT 1
            "#,
            schema = context.schema_name
        );

        let student_id: Option<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(student_id)
    }
}

#[async_trait]
impl DataFilter for EnhancedCasbinDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        self.get_filter_from_casbin_policies(context, casbin_service).await
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        if let Some(condition) = self.get_filter_condition(context, casbin_service).await? {
            // 应用过滤条件到查询构建器
            query_builder.push(" AND ");
            query_builder.push(&condition.sql_condition);

            count_builder.push(" AND ");
            count_builder.push(&condition.sql_condition);

            // 绑定参数
            for param in &condition.bind_params {
                match param {
                    FilterParam::String(s) => {
                        query_builder.push_bind(s);
                        count_builder.push_bind(s);
                    },
                    FilterParam::Uuid(u) => {
                        query_builder.push_bind(u);
                        count_builder.push_bind(u);
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(i);
                        count_builder.push_bind(i);
                    },
                }
            }

            info!("Applied enhanced casbin filter: {}", condition.sql_condition);
            Ok(true)
        } else {
            Ok(false)
        }
    }
}
