use anyhow::{Result, anyhow};
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{info, warn, error};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

use super::{DataPolicy, PolicyBasedDataFilter, FilterContext};

/// 策略管理工具
/// 用于管理、验证和测试casbin数据策略
pub struct PolicyManagementTool {
    pool: PgPool,
}

/// 策略测试结果
#[derive(Debug, Serialize, Deserialize)]
pub struct PolicyTestResult {
    pub user_id: Uuid,
    pub role: String,
    pub resource: String,
    pub action: String,
    pub matched_policy: Option<DataPolicy>,
    pub generated_condition: Option<String>,
    pub test_passed: bool,
    pub error_message: Option<String>,
}

/// 策略统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct PolicyStatistics {
    pub total_policies: i64,
    pub active_policies: i64,
    pub policies_by_role: HashMap<String, i64>,
    pub policies_by_resource: HashMap<String, i64>,
    pub policies_by_scope_type: HashMap<String, i64>,
}

impl PolicyManagementTool {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取策略统计信息
    pub async fn get_policy_statistics(&self) -> Result<PolicyStatistics> {
        info!("Generating policy statistics...");

        // 总策略数
        let total_policies: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.casbin_data_policies"
        )
        .fetch_one(&self.pool)
        .await?;

        // 活跃策略数
        let active_policies: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.casbin_data_policies WHERE is_active = TRUE"
        )
        .fetch_one(&self.pool)
        .await?;

        // 按角色统计
        let role_stats: Vec<(String, i64)> = sqlx::query_as(
            "SELECT subject, COUNT(*) as count FROM public.casbin_data_policies WHERE is_active = TRUE GROUP BY subject ORDER BY count DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        // 按资源统计
        let resource_stats: Vec<(String, i64)> = sqlx::query_as(
            "SELECT object, COUNT(*) as count FROM public.casbin_data_policies WHERE is_active = TRUE GROUP BY object ORDER BY count DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        // 按范围类型统计
        let scope_stats: Vec<(String, i64)> = sqlx::query_as(
            "SELECT scope_type, COUNT(*) as count FROM public.casbin_data_policies WHERE is_active = TRUE GROUP BY scope_type ORDER BY count DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(PolicyStatistics {
            total_policies,
            active_policies,
            policies_by_role: role_stats.into_iter().collect(),
            policies_by_resource: resource_stats.into_iter().collect(),
            policies_by_scope_type: scope_stats.into_iter().collect(),
        })
    }

    /// 测试特定用户的策略
    pub async fn test_user_policy(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        schema_name: &str,
        resource: &str,
        action: &str,
    ) -> Result<PolicyTestResult> {
        info!("Testing policy for user: {}, resource: {}, action: {}", user_id, resource, action);

        // 获取用户角色
        let user_roles = self.get_user_roles(user_id, schema_name).await?;
        if user_roles.is_empty() {
            return Ok(PolicyTestResult {
                user_id,
                role: "none".to_string(),
                resource: resource.to_string(),
                action: action.to_string(),
                matched_policy: None,
                generated_condition: None,
                test_passed: false,
                error_message: Some("User has no roles".to_string()),
            });
        }

        let primary_role = user_roles.first().unwrap().clone();

        // 查询匹配的策略
        let policies = self.query_matching_policies(&user_roles, tenant_id, resource, action).await?;
        
        if policies.is_empty() {
            return Ok(PolicyTestResult {
                user_id,
                role: primary_role,
                resource: resource.to_string(),
                action: action.to_string(),
                matched_policy: None,
                generated_condition: None,
                test_passed: false,
                error_message: Some("No matching policies found".to_string()),
            });
        }

        // 选择最高优先级的策略
        let selected_policy = policies.first().unwrap();

        // 尝试生成SQL条件
        let context = FilterContext {
            user_id,
            tenant_id: tenant_id.to_string(),
            user_identity: format!("user:{}", user_id),
            resource: resource.to_string(),
            action: action.to_string(),
            schema_name: schema_name.to_string(),
        };

        let filter = PolicyBasedDataFilter::new(self.pool.clone());
        
        match filter.get_filter_condition(&context, &MockCasbinService).await {
            Ok(Some(condition)) => {
                Ok(PolicyTestResult {
                    user_id,
                    role: primary_role,
                    resource: resource.to_string(),
                    action: action.to_string(),
                    matched_policy: Some(selected_policy.clone()),
                    generated_condition: Some(condition.sql_condition),
                    test_passed: true,
                    error_message: None,
                })
            },
            Ok(None) => {
                Ok(PolicyTestResult {
                    user_id,
                    role: primary_role,
                    resource: resource.to_string(),
                    action: action.to_string(),
                    matched_policy: Some(selected_policy.clone()),
                    generated_condition: Some("No filter applied (admin user)".to_string()),
                    test_passed: true,
                    error_message: None,
                })
            },
            Err(e) => {
                Ok(PolicyTestResult {
                    user_id,
                    role: primary_role,
                    resource: resource.to_string(),
                    action: action.to_string(),
                    matched_policy: Some(selected_policy.clone()),
                    generated_condition: None,
                    test_passed: false,
                    error_message: Some(e.to_string()),
                })
            }
        }
    }

    /// 批量测试策略
    pub async fn batch_test_policies(
        &self,
        test_cases: Vec<(Uuid, String, String, String, String)>, // (user_id, tenant_id, schema_name, resource, action)
    ) -> Result<Vec<PolicyTestResult>> {
        info!("Running batch policy tests for {} cases", test_cases.len());

        let mut results = Vec::new();
        
        for (user_id, tenant_id, schema_name, resource, action) in test_cases {
            let result = self.test_user_policy(user_id, &tenant_id, &schema_name, &resource, &action).await?;
            results.push(result);
        }

        let passed = results.iter().filter(|r| r.test_passed).count();
        let failed = results.len() - passed;
        
        info!("Batch test completed: {} passed, {} failed", passed, failed);

        Ok(results)
    }

    /// 验证策略配置的完整性
    pub async fn validate_policy_configuration(&self) -> Result<Vec<String>> {
        info!("Validating policy configuration...");

        let mut issues = Vec::new();

        // 检查是否有重复的策略
        let duplicates: Vec<(String, String, String, String, i64)> = sqlx::query_as(
            r#"
            SELECT subject, domain, object, action, COUNT(*) as count
            FROM public.casbin_data_policies
            WHERE is_active = TRUE
            GROUP BY subject, domain, object, action
            HAVING COUNT(*) > 1
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        for (subject, domain, object, action, count) in duplicates {
            issues.push(format!(
                "Duplicate policies found: {} {} {} {} (count: {})",
                subject, domain, object, action, count
            ));
        }

        // 检查是否有无效的SQL模板
        let policies_with_templates: Vec<DataPolicy> = sqlx::query_as(
            "SELECT * FROM public.casbin_data_policies WHERE condition_template IS NOT NULL AND is_active = TRUE"
        )
        .fetch_all(&self.pool)
        .await?;

        for policy in policies_with_templates {
            if let Some(template) = &policy.condition_template {
                if !self.validate_sql_template(template) {
                    issues.push(format!(
                        "Invalid SQL template in policy {}: {}",
                        policy.id, template
                    ));
                }
            }
        }

        // 检查是否缺少基本角色的策略
        let required_roles = vec!["principal", "class_teacher", "student", "parent"];
        let existing_roles: Vec<String> = sqlx::query_scalar(
            "SELECT DISTINCT subject FROM public.casbin_data_policies WHERE is_active = TRUE"
        )
        .fetch_all(&self.pool)
        .await?;

        for required_role in required_roles {
            if !existing_roles.contains(&required_role.to_string()) {
                issues.push(format!("Missing policies for required role: {}", required_role));
            }
        }

        if issues.is_empty() {
            info!("Policy configuration validation passed");
        } else {
            warn!("Policy configuration validation found {} issues", issues.len());
        }

        Ok(issues)
    }

    /// 创建新的策略
    pub async fn create_policy(
        &self,
        subject: &str,
        domain: &str,
        object: &str,
        action: &str,
        scope_type: &str,
        condition_template: Option<&str>,
        priority: i32,
        description: &str,
    ) -> Result<i64> {
        info!("Creating new policy: {} {} {} {}", subject, domain, object, action);

        let policy_id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO public.casbin_data_policies 
            (subject, domain, object, action, scope_type, condition_template, priority, description)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
            "#
        )
        .bind(subject)
        .bind(domain)
        .bind(object)
        .bind(action)
        .bind(scope_type)
        .bind(condition_template)
        .bind(priority)
        .bind(description)
        .fetch_one(&self.pool)
        .await?;

        info!("Created policy with ID: {}", policy_id);
        Ok(policy_id)
    }

    /// 更新策略
    pub async fn update_policy(
        &self,
        policy_id: i64,
        condition_template: Option<&str>,
        priority: Option<i32>,
        description: Option<&str>,
        is_active: Option<bool>,
    ) -> Result<()> {
        info!("Updating policy: {}", policy_id);

        let mut query = "UPDATE public.casbin_data_policies SET updated_at = NOW()".to_string();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Postgres> + Send + Sync>> = Vec::new();
        let mut param_count = 0;

        if let Some(template) = condition_template {
            param_count += 1;
            query.push_str(&format!(", condition_template = ${}", param_count));
            params.push(Box::new(template.to_string()));
        }

        if let Some(p) = priority {
            param_count += 1;
            query.push_str(&format!(", priority = ${}", param_count));
            params.push(Box::new(p));
        }

        if let Some(desc) = description {
            param_count += 1;
            query.push_str(&format!(", description = ${}", param_count));
            params.push(Box::new(desc.to_string()));
        }

        if let Some(active) = is_active {
            param_count += 1;
            query.push_str(&format!(", is_active = ${}", param_count));
            params.push(Box::new(active));
        }

        param_count += 1;
        query.push_str(&format!(" WHERE id = ${}", param_count));

        // 由于参数类型复杂，这里简化实现
        sqlx::query(&format!(
            "UPDATE public.casbin_data_policies SET updated_at = NOW(), condition_template = $1 WHERE id = $2"
        ))
        .bind(condition_template)
        .bind(policy_id)
        .execute(&self.pool)
        .await?;

        info!("Updated policy: {}", policy_id);
        Ok(())
    }

    /// 获取用户角色
    async fn get_user_roles(&self, user_id: Uuid, schema_name: &str) -> Result<Vec<String>> {
        let query = format!(
            r#"
            SELECT DISTINCT r.name
            FROM "{schema}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            "#,
            schema = schema_name
        );

        let roles: Vec<String> = sqlx::query_scalar(&query)
            .bind(user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(roles)
    }

    /// 查询匹配的策略
    async fn query_matching_policies(
        &self,
        user_roles: &[String],
        tenant_id: &str,
        resource: &str,
        action: &str,
    ) -> Result<Vec<DataPolicy>> {
        if user_roles.is_empty() {
            return Ok(vec![]);
        }

        let roles_placeholder = user_roles.iter()
            .map(|_| "?")
            .collect::<Vec<_>>()
            .join(",");

        let query = format!(
            r#"
            SELECT * FROM public.casbin_data_policies
            WHERE subject IN ({})
            AND (domain = ? OR domain = '*')
            AND object = ?
            AND action = ?
            AND effect = 'allow'
            AND is_active = TRUE
            ORDER BY priority DESC, id ASC
            "#,
            roles_placeholder
        );

        let mut query_builder = sqlx::query_as::<_, DataPolicy>(&query);

        for role in user_roles {
            query_builder = query_builder.bind(role);
        }

        query_builder = query_builder
            .bind(tenant_id)
            .bind(resource)
            .bind(action);

        let policies = query_builder.fetch_all(&self.pool).await?;
        Ok(policies)
    }

    /// 验证SQL模板的基本语法
    fn validate_sql_template(&self, template: &str) -> bool {
        // 基本的SQL模板验证
        if template.trim().is_empty() {
            return false;
        }

        // 检查是否包含危险的SQL关键字
        let dangerous_keywords = vec!["DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE"];
        let upper_template = template.to_uppercase();
        
        for keyword in dangerous_keywords {
            if upper_template.contains(keyword) {
                return false;
            }
        }

        // 检查变量格式
        let variables = vec!["${user_id}", "${tenant_id}", "${schema_name}"];
        for var in variables {
            if template.contains(var) {
                // 变量格式正确
                continue;
            }
        }

        true
    }
}

/// Mock Casbin服务用于测试
struct MockCasbinService;

#[async_trait::async_trait]
impl super::CasbinPermissionService for MockCasbinService {
    async fn enforce(&self, _request: &super::PermissionRequest) -> Result<bool> {
        Ok(true)
    }

    async fn get_user_data_scopes(&self, _user_identity: &str, _tenant_id: &str, _resource: &str) -> Result<Vec<super::DataScope>> {
        Ok(vec![])
    }

    async fn get_user_menu_permissions(&self, _user_identity: &str, _tenant_id: &str) -> Result<Vec<super::MenuPermission>> {
        Ok(vec![])
    }
}
