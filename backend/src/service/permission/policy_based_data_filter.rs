use anyhow::{Result, anyhow};
use async_trait::async_trait;
use sqlx::{Pg<PERSON><PERSON>, QueryBuilder, Postgres, FromRow};
use uuid::Uuid;
use tracing::{info, debug, warn, error};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

use super::{CasbinPermissionService, DataFilter, FilterContext, FilterCondition, FilterParam};

/// 数据策略定义
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct DataPolicy {
    pub id: i64,
    pub ptype: String,
    pub subject: String,           // 角色名
    pub domain: String,            // 租户ID
    pub object: String,            // 资源类型
    pub action: String,            // 操作类型
    pub effect: String,            // 效果 (allow/deny)
    pub scope_type: String,        // 范围类型 (school, grade, class, self等)
    pub scope_value: Option<String>, // 范围值 (all, managed, only等)
    pub condition_template: Option<String>, // SQL条件模板
    pub priority: i32,             // 优先级 (数值越大优先级越高)
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 基于策略的数据过滤器（优雅版本）
/// 通过查询casbin_data_policies表获取SQL条件模板，实现更灵活的数据过滤
#[derive(Debug, Clone)]
pub struct PolicyBasedDataFilter {
    pool: PgPool,
    template_cache: std::sync::Arc<std::sync::RwLock<HashMap<String, String>>>,
}

impl PolicyBasedDataFilter {
    pub fn new(pool: PgPool) -> Self {
        Self {
            pool,
            template_cache: std::sync::Arc::new(std::sync::RwLock::new(HashMap::new())),
        }
    }

    /// 从策略中获取数据过滤条件
    async fn get_filter_from_policies(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        info!("PolicyBasedDataFilter: Getting filter from policies for user: {}", context.user_id);

        // 1. 获取用户角色
        let user_roles = self.get_user_roles(context).await?;
        if user_roles.is_empty() {
            warn!("No roles found for user: {}", context.user_id);
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无角色，拒绝访问
                bind_params: vec![],
            }));
        }

        debug!("User roles: {:?}", user_roles);

        // 2. 查询匹配的数据策略
        let policies = self.query_data_policies(
            &user_roles,
            &context.tenant_id,
            &context.resource,
            &context.action,
        ).await?;

        if policies.is_empty() {
            warn!("No matching policies found for user: {}, resource: {}", 
                  context.user_id, context.resource);
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无策略，拒绝访问
                bind_params: vec![],
            }));
        }

        // 3. 按优先级排序，选择最高优先级的策略
        let mut sorted_policies = policies;
        sorted_policies.sort_by(|a, b| b.priority.cmp(&a.priority));

        if let Some(policy) = sorted_policies.first() {
            info!("Selected policy: {} with priority: {}", policy.subject, policy.priority);

            // 4. 渲染SQL条件模板
            if let Some(template) = &policy.condition_template {
                let sql_condition = self.render_condition_template(template, context).await?;
                
                return Ok(Some(FilterCondition {
                    sql_condition,
                    bind_params: vec![], // 参数已经在模板中处理
                }));
            } else {
                // 如果没有条件模板，根据scope_type生成默认条件
                return self.generate_default_condition(policy, context).await;
            }
        }

        // 5. 默认拒绝访问
        Ok(Some(FilterCondition {
            sql_condition: "1=0".to_string(),
            bind_params: vec![],
        }))
    }

    /// 获取用户角色
    async fn get_user_roles(&self, context: &FilterContext) -> Result<Vec<String>> {
        let query = format!(
            r#"
            SELECT DISTINCT r.name as role_name
            FROM "{schema}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            "#,
            schema = context.schema_name
        );

        let roles: Vec<String> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(roles)
    }

    /// 查询数据策略
    async fn query_data_policies(
        &self,
        user_roles: &[String],
        tenant_id: &str,
        resource: &str,
        action: &str,
    ) -> Result<Vec<DataPolicy>> {
        if user_roles.is_empty() {
            return Ok(vec![]);
        }

        let roles_placeholder = user_roles.iter()
            .map(|_| "?")
            .collect::<Vec<_>>()
            .join(",");

        let query = format!(
            r#"
            SELECT * FROM public.casbin_data_policies
            WHERE subject IN ({})
            AND (domain = ? OR domain = '*')
            AND object = ?
            AND action = ?
            AND effect = 'allow'
            ORDER BY priority DESC, id ASC
            "#,
            roles_placeholder
        );

        let mut query_builder = sqlx::query_as::<_, DataPolicy>(&query);

        // 绑定角色参数
        for role in user_roles {
            query_builder = query_builder.bind(role);
        }

        // 绑定其他参数
        query_builder = query_builder
            .bind(tenant_id)
            .bind(resource)
            .bind(action);

        let policies = query_builder.fetch_all(&self.pool).await?;
        debug!("Found {} matching policies", policies.len());

        Ok(policies)
    }

    /// 渲染SQL条件模板
    async fn render_condition_template(
        &self,
        template: &str,
        context: &FilterContext,
    ) -> Result<String> {
        let mut rendered = template.to_string();

        // 替换基本变量
        rendered = rendered.replace("${user_id}", &format!("'{}'", context.user_id));
        rendered = rendered.replace("${tenant_id}", &format!("'{}'", context.tenant_id));
        rendered = rendered.replace("${schema_name}", &format!("\"{}\"", context.schema_name));

        // 处理更复杂的变量替换
        rendered = self.process_advanced_variables(rendered, context).await?;

        debug!("Rendered SQL condition: {}", rendered);
        Ok(rendered)
    }

    /// 处理高级变量替换
    async fn process_advanced_variables(
        &self,
        mut template: String,
        context: &FilterContext,
    ) -> Result<String> {
        // 处理 ${user.managed_classes} 等复杂变量
        if template.contains("${user.managed_classes}") {
            let managed_classes = self.get_user_managed_classes(context).await?;
            let classes_str = managed_classes.iter()
                .map(|id| format!("'{}'", id))
                .collect::<Vec<_>>()
                .join(",");
            template = template.replace("${user.managed_classes}", &classes_str);
        }

        if template.contains("${user.managed_grades}") {
            let managed_grades = self.get_user_managed_grades(context).await?;
            let grades_str = managed_grades.iter()
                .map(|id| format!("'{}'", id))
                .collect::<Vec<_>>()
                .join(",");
            template = template.replace("${user.managed_grades}", &grades_str);
        }

        if template.contains("${user.student_id}") {
            if let Some(student_id) = self.get_user_student_id(context).await? {
                template = template.replace("${user.student_id}", &format!("'{}'", student_id));
            }
        }

        Ok(template)
    }

    /// 获取用户管理的班级ID列表
    async fn get_user_managed_classes(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'class' AND target_id IS NOT NULL
            "#,
            schema = context.schema_name
        );

        let class_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(class_ids)
    }

    /// 获取用户管理的年级ID列表
    async fn get_user_managed_grades(&self, context: &FilterContext) -> Result<Vec<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'grade' AND target_id IS NOT NULL
            "#,
            schema = context.schema_name
        );

        let grade_ids: Vec<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_all(&self.pool)
            .await?;

        Ok(grade_ids)
    }

    /// 获取用户对应的学生ID
    async fn get_user_student_id(&self, context: &FilterContext) -> Result<Option<Uuid>> {
        let query = format!(
            r#"
            SELECT target_id
            FROM "{schema}".user_identities
            WHERE user_id = $1 AND target_type = 'student' AND target_id IS NOT NULL
            LIMIT 1
            "#,
            schema = context.schema_name
        );

        let student_id: Option<Uuid> = sqlx::query_scalar(&query)
            .bind(context.user_id)
            .fetch_optional(&self.pool)
            .await?;

        Ok(student_id)
    }

    /// 生成默认条件（当没有模板时）
    async fn generate_default_condition(
        &self,
        policy: &DataPolicy,
        context: &FilterContext,
    ) -> Result<Option<FilterCondition>> {
        match policy.scope_type.as_str() {
            "school" => {
                // 校级权限：无限制
                Ok(Some(FilterCondition {
                    sql_condition: "1=1".to_string(),
                    bind_params: vec![],
                }))
            },
            "grade" => {
                // 年级权限：限制到管理的年级
                let managed_grades = self.get_user_managed_grades(context).await?;
                if managed_grades.is_empty() {
                    return Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }));
                }

                let condition = format!(
                    "s.grade_id IN ({})",
                    managed_grades.iter().map(|_| "?").collect::<Vec<_>>().join(",")
                );

                let params = managed_grades.into_iter()
                    .map(FilterParam::Uuid)
                    .collect();

                Ok(Some(FilterCondition {
                    sql_condition: condition,
                    bind_params: params,
                }))
            },
            "class" => {
                // 班级权限：限制到管理的班级
                let managed_classes = self.get_user_managed_classes(context).await?;
                if managed_classes.is_empty() {
                    return Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }));
                }

                let condition = format!(
                    "s.administrative_class_id IN ({})",
                    managed_classes.iter().map(|_| "?").collect::<Vec<_>>().join(",")
                );

                let params = managed_classes.into_iter()
                    .map(FilterParam::Uuid)
                    .collect();

                Ok(Some(FilterCondition {
                    sql_condition: condition,
                    bind_params: params,
                }))
            },
            "self" => {
                // 自己的数据：限制到用户对应的学生记录
                if let Some(student_id) = self.get_user_student_id(context).await? {
                    Ok(Some(FilterCondition {
                        sql_condition: "s.id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(student_id)],
                    }))
                } else {
                    Ok(Some(FilterCondition {
                        sql_condition: "1=0".to_string(),
                        bind_params: vec![],
                    }))
                }
            },
            _ => {
                warn!("Unknown scope type: {}", policy.scope_type);
                Ok(Some(FilterCondition {
                    sql_condition: "1=0".to_string(),
                    bind_params: vec![],
                }))
            }
        }
    }
}

#[async_trait]
impl DataFilter for PolicyBasedDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        self.get_filter_from_policies(context, casbin_service).await
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        if let Some(condition) = self.get_filter_condition(context, casbin_service).await? {
            // 应用过滤条件到查询构建器
            query_builder.push(" AND ");
            query_builder.push(&condition.sql_condition);

            count_builder.push(" AND ");
            count_builder.push(&condition.sql_condition);

            // 绑定参数
            for param in &condition.bind_params {
                match param {
                    FilterParam::String(s) => {
                        query_builder.push_bind(s);
                        count_builder.push_bind(s);
                    },
                    FilterParam::Uuid(u) => {
                        query_builder.push_bind(u);
                        count_builder.push_bind(u);
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(i);
                        count_builder.push_bind(i);
                    },
                }
            }

            info!("Applied policy-based filter: {}", condition.sql_condition);
            Ok(true)
        } else {
            Ok(false)
        }
    }
}
