use anyhow::{Result, anyhow};
use async_trait::async_trait;
use sqlx::{PgPool, QueryBuilder, Postgres};
use uuid::Uuid;
use tracing::{info, debug, warn, error};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

use super::{CasbinPermissionService, DataFilter, FilterContext, FilterCondition, FilterParam};

/// 基于用户身份的数据范围推导器
/// 当casbin_policies只定义通配符权限策略时，通过用户身份来推导具体的数据访问范围
#[derive(Debug, Clone)]
pub struct IdentityBasedDataFilter {
    pool: PgPool,
}

/// 用户身份信息
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct UserIdentityInfo {
    pub identity_id: Uuid,
    pub user_id: Uuid,
    pub role_id: Uuid,
    pub role_name: String,
    pub target_type: String,    // 'school', 'subject_group', 'grade', 'class', 'student'
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    pub tenant_id: String,
}

/// 数据范围推导规则
#[derive(Debug, Clone)]
pub struct DataScopeRule {
    pub role_name: String,
    pub target_type: String,
    pub resource: String,
    pub scope_generator: fn(&UserIdentityInfo, &str) -> Result<Vec<FilterCondition>>,
}

impl IdentityBasedDataFilter {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取用户在指定租户中的所有身份信息
    async fn get_user_identities(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        schema_name: &str,
    ) -> Result<Vec<UserIdentityInfo>> {
        let query = format!(
            r#"
            SELECT 
                ui.id as identity_id,
                ui.user_id,
                ui.role_id,
                r.name as role_name,
                ui.target_type,
                ui.target_id,
                ui.subject,
                $1 as tenant_id
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $2
            ORDER BY ui.created_at DESC
            "#
        );

        let identities = sqlx::query_as::<_, UserIdentityInfo>(&query)
            .bind(tenant_id)
            .bind(user_id)
            .fetch_all(&self.pool)
            .await?;

        debug!("Found {} identities for user {} in tenant {}", 
               identities.len(), user_id, tenant_id);

        Ok(identities)
    }

    /// 基于用户身份推导数据访问范围
    async fn derive_data_scopes_from_identities(
        &self,
        identities: &[UserIdentityInfo],
        resource: &str,
        action: &str,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Vec<FilterCondition>> {
        let mut conditions = Vec::new();

        for identity in identities {
            // 1. 检查用户是否有该资源的通配符权限
            let user_identity = format!("user:{}", identity.user_id);
            let permission_request = crate::service::permission::PermissionRequest {
                subject: user_identity,
                domain: identity.tenant_id.clone(),
                object: format!("{}:*", resource),
                action: action.to_string(),
            };

            if !casbin_service.enforce(&permission_request).await? {
                debug!("User {} has no {} permission for resource {}", 
                       identity.user_id, action, resource);
                continue;
            }

            // 2. 基于身份类型推导具体的数据范围
            let scope_conditions = self.generate_scope_conditions_by_identity(identity, resource).await?;
            conditions.extend(scope_conditions);
        }

        Ok(conditions)
    }

    /// 根据用户身份生成数据范围条件
    async fn generate_scope_conditions_by_identity(
        &self,
        identity: &UserIdentityInfo,
        resource: &str,
    ) -> Result<Vec<FilterCondition>> {
        let mut conditions = Vec::new();

        match (identity.role_name.as_str(), identity.target_type.as_str(), resource) {
            // 校长 - 可以访问整个学校的数据
            ("principal", "school", "student") => {
                conditions.push(FilterCondition {
                    sql_condition: "1=1".to_string(), // 无限制
                    bind_params: vec![],
                });
            },

            // 年级长 - 可以访问所负责年级的学生数据
            ("grade_director", "grade", "student") => {
                if let Some(grade_id) = identity.target_id {
                    conditions.push(FilterCondition {
                        sql_condition: "s.grade_id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(grade_id)],
                    });
                }
            },

            // 班主任 - 可以访问所负责班级的学生数据
            ("class_teacher", "class", "student") => {
                if let Some(class_id) = identity.target_id {
                    conditions.push(FilterCondition {
                        sql_condition: "s.administrative_class_id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(class_id)],
                    });
                }
            },

            // 任课老师 - 可以访问所教授班级的学生数据
            ("subject_teacher", "class", "student") => {
                if let Some(class_id) = identity.target_id {
                    // 需要通过教学关系表来确定
                    let teaching_condition = self.get_teaching_classes_condition(
                        identity.user_id, 
                        identity.subject.as_deref(),
                        &identity.tenant_id
                    ).await?;
                    if let Some(condition) = teaching_condition {
                        conditions.push(condition);
                    }
                }
            },

            // 学科组长 - 可以访问该学科相关的数据
            ("subject_group_leader", "subject_group", "student") => {
                if let Some(subject_group_id) = identity.target_id {
                    // 通过学科组关联的班级来过滤学生
                    let subject_condition = self.get_subject_group_classes_condition(
                        subject_group_id,
                        &identity.tenant_id
                    ).await?;
                    if let Some(condition) = subject_condition {
                        conditions.push(condition);
                    }
                }
            },

            // 学生 - 只能访问自己的数据
            ("student", "student", "student") => {
                if let Some(student_id) = identity.target_id {
                    conditions.push(FilterCondition {
                        sql_condition: "s.id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(student_id)],
                    });
                }
            },

            // 家长 - 只能访问自己孩子的数据
            ("parent", "student", "student") => {
                if let Some(student_id) = identity.target_id {
                    conditions.push(FilterCondition {
                        sql_condition: "s.id = ?".to_string(),
                        bind_params: vec![FilterParam::Uuid(student_id)],
                    });
                }
            },

            _ => {
                warn!("Unhandled identity combination: role={}, target_type={}, resource={}", 
                      identity.role_name, identity.target_type, resource);
            }
        }

        Ok(conditions)
    }

    /// 获取教师任教班级的过滤条件
    async fn get_teaching_classes_condition(
        &self,
        teacher_user_id: Uuid,
        subject: Option<&str>,
        tenant_id: &str,
    ) -> Result<Option<FilterCondition>> {
        // 这里需要根据您的教学关系表结构来实现
        // 假设有一个 teacher_class_subjects 表记录教师-班级-学科关系
        
        let mut query_builder = QueryBuilder::new(
            "SELECT DISTINCT tc.class_id FROM teacher_class_subjects tc WHERE tc.teacher_user_id = "
        );
        query_builder.push_bind(teacher_user_id);
        
        if let Some(subj) = subject {
            query_builder.push(" AND tc.subject = ");
            query_builder.push_bind(subj);
        }
        
        // 这里简化处理，实际需要执行查询获取班级ID列表
        // 然后构建 IN 条件
        
        Ok(Some(FilterCondition {
            sql_condition: "s.administrative_class_id IN (SELECT class_id FROM teacher_class_subjects WHERE teacher_user_id = ?)".to_string(),
            bind_params: vec![FilterParam::Uuid(teacher_user_id)],
        }))
    }

    /// 获取学科组相关班级的过滤条件
    async fn get_subject_group_classes_condition(
        &self,
        subject_group_id: Uuid,
        tenant_id: &str,
    ) -> Result<Option<FilterCondition>> {
        // 根据学科组获取相关班级
        Ok(Some(FilterCondition {
            sql_condition: "s.administrative_class_id IN (SELECT class_id FROM subject_group_classes WHERE subject_group_id = ?)".to_string(),
            bind_params: vec![FilterParam::Uuid(subject_group_id)],
        }))
    }
}

#[async_trait]
impl DataFilter for IdentityBasedDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        info!("IdentityBasedDataFilter: Processing user_id={}, resource={}", 
              context.user_id, context.resource);

        // 1. 获取用户在当前租户的所有身份
        let identities = self.get_user_identities(
            context.user_id,
            &context.tenant_id,
            &context.schema_name,
        ).await?;

        if identities.is_empty() {
            warn!("No identities found for user {} in tenant {}", 
                  context.user_id, context.tenant_id);
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无权限访问
                bind_params: vec![],
            }));
        }

        // 2. 基于身份推导数据访问范围
        let conditions = self.derive_data_scopes_from_identities(
            &identities,
            &context.resource,
            &context.action,
            casbin_service,
        ).await?;

        if conditions.is_empty() {
            return Ok(Some(FilterCondition {
                sql_condition: "1=0".to_string(), // 无权限访问
                bind_params: vec![],
            }));
        }

        // 3. 合并多个条件（OR关系）
        if conditions.len() == 1 {
            Ok(Some(conditions[0].clone()))
        } else {
            let sql_parts: Vec<String> = conditions.iter()
                .map(|c| format!("({})", c.sql_condition))
                .collect();
            let combined_sql = sql_parts.join(" OR ");
            
            let mut all_params = Vec::new();
            for condition in conditions {
                all_params.extend(condition.bind_params);
            }

            Ok(Some(FilterCondition {
                sql_condition: format!("({})", combined_sql),
                bind_params: all_params,
            }))
        }
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        if let Some(condition) = self.get_filter_condition(context, casbin_service).await? {
            // 应用过滤条件到查询构建器
            query_builder.push(" AND ");
            query_builder.push(&condition.sql_condition);
            
            count_builder.push(" AND ");
            count_builder.push(&condition.sql_condition);

            // 绑定参数
            for param in &condition.bind_params {
                match param {
                    FilterParam::String(s) => {
                        query_builder.push_bind(s);
                        count_builder.push_bind(s);
                    },
                    FilterParam::Uuid(u) => {
                        query_builder.push_bind(u);
                        count_builder.push_bind(u);
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(i);
                        count_builder.push_bind(i);
                    },
                }
            }

            info!("Applied identity-based filter: {}", condition.sql_condition);
            Ok(true)
        } else {
            Ok(false)
        }
    }
}
