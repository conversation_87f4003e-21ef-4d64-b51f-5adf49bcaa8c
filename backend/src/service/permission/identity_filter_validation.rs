use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{info, warn, error};
use std::sync::Arc;

use super::{
    IdentityBasedDataFilter,
    FilterContext,
    CasbinPermissionService,
    DataFilter,
};

/// 验证基于身份的数据过滤器实现
pub struct IdentityFilterValidator {
    pool: PgPool,
    filter: Arc<IdentityBasedDataFilter>,
}

impl IdentityFilterValidator {
    pub fn new(pool: PgPool) -> Self {
        let filter = Arc::new(IdentityBasedDataFilter::new(pool.clone()));
        Self { pool, filter }
    }

    /// 验证用户身份查询是否正常工作
    pub async fn validate_user_identity_query(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        schema_name: &str,
    ) -> Result<bool> {
        info!("Validating user identity query for user: {}, tenant: {}", user_id, tenant_id);

        // 直接调用get_user_identities方法（需要将其设为public或添加测试方法）
        let query = format!(
            r#"
            SELECT 
                ui.id as identity_id,
                ui.user_id,
                ui.role_id,
                r.name as role_name,
                ui.target_type,
                ui.target_id,
                ui.subject,
                $1 as tenant_id
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $2
            ORDER BY ui.created_at DESC
            "#
        );

        let result = sqlx::query(&query)
            .bind(tenant_id)
            .bind(user_id)
            .fetch_all(&self.pool)
            .await;

        match result {
            Ok(rows) => {
                info!("Found {} identity records for user {}", rows.len(), user_id);
                Ok(rows.len() > 0)
            },
            Err(e) => {
                error!("Failed to query user identities: {}", e);
                Ok(false)
            }
        }
    }

    /// 验证过滤条件生成
    pub async fn validate_filter_condition_generation(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        info!("Validating filter condition generation for user: {}", context.user_id);

        match self.filter.get_filter_condition(context, casbin_service).await {
            Ok(Some(condition)) => {
                info!("Generated filter condition: {}", condition.sql_condition);
                info!("Bind parameters count: {}", condition.bind_params.len());
                Ok(true)
            },
            Ok(None) => {
                info!("No filter condition generated (admin user or no restrictions)");
                Ok(true)
            },
            Err(e) => {
                error!("Failed to generate filter condition: {}", e);
                Ok(false)
            }
        }
    }

    /// 验证不同身份类型的过滤逻辑
    pub async fn validate_identity_types(
        &self,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<()> {
        info!("Validating different identity types...");

        // 测试用例：不同身份类型的用户
        let test_cases = vec![
            (Uuid::new_v4(), "principal", "校长应该能访问所有数据"),
            (Uuid::new_v4(), "grade_director", "年级长应该只能访问所负责年级的数据"),
            (Uuid::new_v4(), "class_teacher", "班主任应该只能访问所负责班级的数据"),
            (Uuid::new_v4(), "subject_teacher", "任课老师应该只能访问所教授班级的数据"),
            (Uuid::new_v4(), "student", "学生应该只能访问自己的数据"),
            (Uuid::new_v4(), "parent", "家长应该只能访问孩子的数据"),
        ];

        for (user_id, expected_role, description) in test_cases {
            info!("Testing: {}", description);

            let context = FilterContext {
                user_id,
                tenant_id: tenant_id.to_string(),
                user_identity: format!("user:{}", user_id),
                resource: "student".to_string(),
                action: "read".to_string(),
                schema_name: schema_name.to_string(),
            };

            let validation_result = self.validate_filter_condition_generation(&context, casbin_service).await?;
            
            if validation_result {
                info!("✅ Validation passed for {}", expected_role);
            } else {
                warn!("❌ Validation failed for {}", expected_role);
            }
        }

        Ok(())
    }

    /// 验证SQL查询构建
    pub async fn validate_sql_query_building(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        info!("Validating SQL query building...");

        use sqlx::{QueryBuilder, Postgres};

        let mut query_builder = QueryBuilder::new(
            format!("SELECT s.* FROM {}.students s WHERE 1=1", context.schema_name)
        );
        let mut count_builder = QueryBuilder::new(
            format!("SELECT COUNT(*) FROM {}.students s WHERE 1=1", context.schema_name)
        );

        match self.filter.apply_filter_to_builders(
            context,
            &mut query_builder,
            &mut count_builder,
            casbin_service,
        ).await {
            Ok(filter_applied) => {
                info!("Filter application result: {}", filter_applied);
                
                // 尝试构建查询（不执行）
                let query_sql = query_builder.sql();
                let count_sql = count_builder.sql();
                
                info!("Generated query SQL: {}", query_sql);
                info!("Generated count SQL: {}", count_sql);
                
                Ok(true)
            },
            Err(e) => {
                error!("Failed to apply filter to builders: {}", e);
                Ok(false)
            }
        }
    }

    /// 完整的验证流程
    pub async fn run_full_validation(
        &self,
        user_id: Uuid,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        info!("🔍 Starting full validation for identity-based data filtering...");

        let mut all_passed = true;

        // 1. 验证用户身份查询
        info!("1️⃣ Validating user identity query...");
        let identity_query_ok = self.validate_user_identity_query(user_id, tenant_id, schema_name).await?;
        if !identity_query_ok {
            warn!("❌ User identity query validation failed");
            all_passed = false;
        } else {
            info!("✅ User identity query validation passed");
        }

        // 2. 验证过滤条件生成
        info!("2️⃣ Validating filter condition generation...");
        let context = FilterContext {
            user_id,
            tenant_id: tenant_id.to_string(),
            user_identity: format!("user:{}", user_id),
            resource: "student".to_string(),
            action: "read".to_string(),
            schema_name: schema_name.to_string(),
        };

        let condition_ok = self.validate_filter_condition_generation(&context, casbin_service).await?;
        if !condition_ok {
            warn!("❌ Filter condition generation validation failed");
            all_passed = false;
        } else {
            info!("✅ Filter condition generation validation passed");
        }

        // 3. 验证SQL查询构建
        info!("3️⃣ Validating SQL query building...");
        let sql_ok = self.validate_sql_query_building(&context, casbin_service).await?;
        if !sql_ok {
            warn!("❌ SQL query building validation failed");
            all_passed = false;
        } else {
            info!("✅ SQL query building validation passed");
        }

        // 4. 验证不同身份类型
        info!("4️⃣ Validating different identity types...");
        if let Err(e) = self.validate_identity_types(tenant_id, schema_name, casbin_service).await {
            warn!("❌ Identity types validation failed: {}", e);
            all_passed = false;
        } else {
            info!("✅ Identity types validation passed");
        }

        if all_passed {
            info!("🎉 All validations passed! Identity-based data filtering is working correctly.");
        } else {
            warn!("⚠️ Some validations failed. Please check the implementation.");
        }

        Ok(all_passed)
    }
}

/// 便捷函数：快速验证身份过滤器
pub async fn quick_validate_identity_filter(
    pool: PgPool,
    user_id: Uuid,
    tenant_id: &str,
    schema_name: &str,
    casbin_service: &dyn CasbinPermissionService,
) -> Result<bool> {
    let validator = IdentityFilterValidator::new(pool);
    validator.run_full_validation(user_id, tenant_id, schema_name, casbin_service).await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_identity_filter_validation() {
        // 这里可以添加具体的测试用例
        // 需要模拟数据库和casbin服务
    }
}
