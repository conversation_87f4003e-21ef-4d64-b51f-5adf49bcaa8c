pub mod casbin_service;
pub mod role_sync_service;
pub mod menu_role_permission_service;
pub mod postgres_adapter;
pub mod data_filter;
pub mod data_scope_sync_service;
pub mod optimized_student_filter;
pub mod class_teacher_permission_manager;
pub mod menu_migration_service;
pub mod identity_based_data_filter;
pub mod identity_filter_integration_example;
pub mod identity_filter_config;
pub mod identity_filter_validation;
pub mod policy_based_data_filter;
pub mod policy_management_tool;
pub mod enhanced_casbin_data_filter;

pub use casbin_service::{
    CasbinPermissionService,
    MultiTenantCasbinService,
    PermissionRequest,
    PermissionPolicy,
    RoleRelation,
    MenuPermission,
    DataScope,
};

pub use role_sync_service::{
    RolePermissionSyncService,
    SyncConfig,
    SyncResult,
};

pub use postgres_adapter::{
    PostgresAdapter,
    CasbinRule,
};

pub use data_filter::{
    DataFilter,
    DataFilterManager,
    FilterContext,
    FilterCondition,
    FilterParam,
    BaseDataFilter,
    StudentDataFilter,
    AdministrativeClassDataFilter,
};

pub use identity_based_data_filter::{
    IdentityBasedDataFilter,
    UserIdentityInfo,
    DataScopeRule,
};

pub use identity_filter_config::{
    IdentityFilterConfig,
    initialize_identity_filtering_system,
};

pub use identity_filter_integration_example::{
    StudentQueryServiceWithIdentityFilter,
    StudentInfo,
    setup_identity_based_data_filters,
};

pub use identity_filter_validation::{
    IdentityFilterValidator,
    quick_validate_identity_filter,
};

pub use policy_based_data_filter::{
    PolicyBasedDataFilter,
    DataPolicy,
};

pub use policy_management_tool::{
    PolicyManagementTool,
    PolicyTestResult,
    PolicyStatistics,
};

pub use enhanced_casbin_data_filter::{
    EnhancedCasbinDataFilter,
    EnhancedCasbinPolicy,
};

pub use data_scope_sync_service::{
    DataScopePermissionSyncService,
    DataScopeSyncConfig,
    DataScopeSyncResult,
};

pub use optimized_student_filter::OptimizedStudentDataFilter;

pub use class_teacher_permission_manager::{
    ClassTeacherPermissionManager,
    ClassTeacherPermission,
    PermissionSyncResult,
};

pub use menu_migration_service::{
    MenuMigrationService,
    MigrationReport,
};

