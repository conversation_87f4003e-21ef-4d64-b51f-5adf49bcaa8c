use std::sync::Arc;
use sqlx::PgPool;

use crate::service::permission::{
    DataFilterManager, StudentDataFilter, IdentityBasedDataFilter, PolicyBasedDataFilter, EnhancedCasbinDataFilter
};
use super::StudentService;

/// 学生服务工厂
pub struct StudentServiceFactory;

impl StudentServiceFactory {
    /// 创建带有增强Casbin过滤器的学生服务（最新推荐）
    pub fn create_with_enhanced_casbin_filter(db_pool: PgPool) -> StudentService {
        let mut filter_manager = DataFilterManager::new();

        // 注册增强的Casbin数据过滤器
        let enhanced_filter = Arc::new(EnhancedCasbinDataFilter::new(db_pool.clone()));
        filter_manager.register_filter("student".to_string(), enhanced_filter);

        StudentService::with_data_filter_manager(
            db_pool,
            Arc::new(filter_manager)
        )
    }

    /// 创建带有策略过滤器的学生服务
    pub fn create_with_policy_based_filter(db_pool: PgPool) -> StudentService {
        let mut filter_manager = DataFilterManager::new();

        // 注册基于策略的数据过滤器
        let policy_filter = Arc::new(PolicyBasedDataFilter::new(db_pool.clone()));
        filter_manager.register_filter("student".to_string(), policy_filter);

        StudentService::with_data_filter_manager(
            db_pool,
            Arc::new(filter_manager)
        )
    }

    /// 创建带有基于身份的数据过滤器的学生服务
    pub fn create_with_identity_based_filter(db_pool: PgPool) -> StudentService {
        let mut filter_manager = DataFilterManager::new();

        // 注册基于身份的数据过滤器
        let identity_filter = Arc::new(IdentityBasedDataFilter::new(db_pool.clone()));
        filter_manager.register_filter("student".to_string(), identity_filter);

        StudentService::with_data_filter_manager(
            db_pool,
            Arc::new(filter_manager)
        )
    }

    /// 创建带有传统数据过滤器的学生服务（向后兼容）
    pub fn create_with_data_filter(db_pool: PgPool) -> StudentService {
        let mut filter_manager = DataFilterManager::new();

        // 注册传统学生数据过滤器
        let student_filter = Arc::new(StudentDataFilter::new(db_pool.clone()));
        filter_manager.register_filter("student".to_string(), student_filter);

        StudentService::with_data_filter_manager(
            db_pool,
            Arc::new(filter_manager)
        )
    }
    
    /// 创建不带数据过滤器的学生服务（向后兼容）
    pub fn create_legacy(db_pool: PgPool) -> StudentService {
        StudentService::new(db_pool)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    
    // 注意：这些测试需要数据库连接，在实际环境中运行
    
    #[tokio::test]
    #[ignore] // 需要数据库连接
    async fn test_create_with_data_filter() {
        // 这里需要一个测试数据库连接
        // let pool = PgPool::connect("postgresql://...").await.unwrap();
        // let service = StudentServiceFactory::create_with_data_filter(pool);
        // assert!(service.data_filter_manager.is_some());
    }
    
    #[tokio::test]
    #[ignore] // 需要数据库连接
    async fn test_create_legacy() {
        // 这里需要一个测试数据库连接
        // let pool = PgPool::connect("postgresql://...").await.unwrap();
        // let service = StudentServiceFactory::create_legacy(pool);
        // assert!(service.data_filter_manager.is_none());
    }
}
