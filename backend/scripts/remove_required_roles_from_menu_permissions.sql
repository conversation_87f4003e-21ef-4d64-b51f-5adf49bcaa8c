-- 从 menu_permissions 表中移除 required_roles 字段
-- 现在角色要求完全从 casbin_policies 表中获取
-- 执行时间: 2025-08-09

-- ================================================================
-- 1. 备份现有的 required_roles 数据到临时表（可选）
-- ================================================================

-- 创建备份表
CREATE TABLE IF NOT EXISTS public.menu_permissions_required_roles_backup AS
SELECT menu_id, required_roles, created_at, updated_at
FROM public.menu_permissions
WHERE required_roles IS NOT NULL AND array_length(required_roles, 1) > 0;

-- 显示备份的数据
SELECT 
    '=== 备份的 required_roles 数据 ===' as section,
    menu_id,
    required_roles,
    array_length(required_roles, 1) as roles_count
FROM public.menu_permissions_required_roles_backup
ORDER BY menu_id;

-- ================================================================
-- 2. 验证 Casbin 策略中是否有对应的角色权限
-- ================================================================

-- 检查哪些菜单在 menu_permissions 中有 required_roles 但在 casbin_policies 中没有对应策略
WITH menu_roles AS (
    SELECT 
        menu_id,
        unnest(required_roles) as role_code
    FROM public.menu_permissions
    WHERE required_roles IS NOT NULL AND array_length(required_roles, 1) > 0
),
casbin_menu_roles AS (
    SELECT 
        SUBSTRING(v2 FROM 'menu:(.*)') as menu_id,
        SUBSTRING(v0 FROM 'role:(.*)') as role_code
    FROM public.casbin_policies
    WHERE ptype = 'p' 
      AND v0 LIKE 'role:%' 
      AND v2 LIKE 'menu:%' 
      AND v3 = 'access'
      AND v4 = 'allow'
)
SELECT 
    '=== 缺失的 Casbin 策略 ===' as section,
    mr.menu_id,
    mr.role_code,
    CASE 
        WHEN cmr.role_code IS NULL THEN '❌ 缺失策略'
        ELSE '✅ 策略存在'
    END as policy_status
FROM menu_roles mr
LEFT JOIN casbin_menu_roles cmr ON mr.menu_id = cmr.menu_id AND mr.role_code = cmr.role_code
ORDER BY mr.menu_id, mr.role_code;

-- ================================================================
-- 3. 移除 required_roles 字段
-- ================================================================

-- 删除 required_roles 字段
ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS required_roles;

-- 验证字段已被删除
SELECT 
    '=== 字段删除验证 ===' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'menu_permissions'
  AND column_name = 'required_roles';

-- 如果上面的查询返回空结果，说明字段已成功删除

-- ================================================================
-- 4. 验证修改后的表结构
-- ================================================================

SELECT 
    '=== 修改后的表结构 ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'menu_permissions'
ORDER BY ordinal_position;

-- ================================================================
-- 5. 测试从 Casbin 策略中获取角色要求
-- ================================================================

-- 测试获取 administrative_classes_management 菜单的角色要求
SELECT 
    '=== 从 Casbin 策略获取角色要求测试 ===' as section,
    SUBSTRING(v0 FROM 'role:(.*)') as role_code,
    v2 as menu_object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies
WHERE ptype = 'p'
  AND v2 = 'menu:administrative_classes_management'
  AND v3 = 'access'
  AND v4 = 'allow'
  AND v0 LIKE 'role:%'
ORDER BY role_code;

-- ================================================================
-- 6. 统计信息
-- ================================================================

SELECT 
    '=== 统计信息 ===' as section,
    'Casbin菜单策略总数' as metric,
    COUNT(*) as count
FROM public.casbin_policies
WHERE ptype = 'p' 
  AND v2 LIKE 'menu:%' 
  AND v3 = 'access'

UNION ALL

SELECT 
    '=== 统计信息 ===' as section,
    '备份的菜单角色数据' as metric,
    COUNT(*) as count
FROM public.menu_permissions_required_roles_backup

UNION ALL

SELECT 
    '=== 统计信息 ===' as section,
    '当前菜单总数' as metric,
    COUNT(*) as count
FROM public.menu_permissions;

-- ================================================================
-- 7. 清理说明
-- ================================================================

-- 注意：备份表 menu_permissions_required_roles_backup 保留用于回滚
-- 如果确认一切正常，可以手动删除：
-- DROP TABLE IF EXISTS public.menu_permissions_required_roles_backup;
