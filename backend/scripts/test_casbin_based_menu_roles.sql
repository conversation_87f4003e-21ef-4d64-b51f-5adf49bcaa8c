-- 测试基于 Casbin 策略的菜单角色获取功能
-- 验证从 casbin_policies 表中正确获取菜单的角色要求

-- ================================================================
-- 1. 测试获取菜单角色要求的SQL函数
-- ================================================================

-- 创建一个函数来模拟后端的 get_menu_required_roles 方法
CREATE OR REPLACE FUNCTION get_menu_required_roles(menu_id_param TEXT)
RETURNS TEXT[] AS $$
DECLARE
    roles TEXT[];
BEGIN
    SELECT ARRAY_AGG(SUBSTRING(v0 FROM 'role:(.*)'))
    INTO roles
    FROM public.casbin_policies
    WHERE ptype = 'p'
      AND v2 = 'menu:' || menu_id_param
      AND v3 = 'access'
      AND v4 = 'allow'
      AND v0 LIKE 'role:%';
    
    RETURN COALESCE(roles, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- 2. 测试各个菜单的角色要求获取
-- ================================================================

SELECT 
    '=== 菜单角色要求测试 ===' as section,
    menu_id,
    name,
    get_menu_required_roles(menu_id) as required_roles_from_casbin,
    array_length(get_menu_required_roles(menu_id), 1) as roles_count
FROM public.menu_permissions
WHERE menu_id IN (
    'administrative_classes_management',
    'student_management', 
    'grade_score_management',
    'teaching_aids_management',
    'exam_management'
)
ORDER BY menu_id;

-- ================================================================
-- 3. 对比备份数据与 Casbin 策略
-- ================================================================

WITH backup_data AS (
    SELECT 
        menu_id,
        required_roles as backup_roles
    FROM public.menu_permissions_required_roles_backup
),
casbin_data AS (
    SELECT 
        menu_id,
        get_menu_required_roles(menu_id) as casbin_roles
    FROM public.menu_permissions
)
SELECT 
    '=== 数据对比分析 ===' as section,
    COALESCE(b.menu_id, c.menu_id) as menu_id,
    b.backup_roles,
    c.casbin_roles,
    CASE 
        WHEN b.backup_roles IS NULL THEN '仅在Casbin中'
        WHEN c.casbin_roles = ARRAY[]::TEXT[] THEN '仅在备份中'
        WHEN b.backup_roles = c.casbin_roles THEN '✅ 完全匹配'
        ELSE '⚠️ 不匹配'
    END as comparison_result
FROM backup_data b
FULL OUTER JOIN casbin_data c ON b.menu_id = c.menu_id
WHERE COALESCE(b.menu_id, c.menu_id) IN (
    'administrative_classes_management',
    'student_management', 
    'grade_score_management',
    'teaching_aids_management',
    'exam_management'
)
ORDER BY menu_id;

-- ================================================================
-- 4. 验证特定角色的菜单访问权限
-- ================================================================

-- 测试 class_teacher 角色可以访问的菜单
SELECT 
    '=== class_teacher 角色可访问菜单 ===' as section,
    SUBSTRING(v2 FROM 'menu:(.*)') as menu_id,
    v3 as action,
    v4 as effect
FROM public.casbin_policies
WHERE ptype = 'p'
  AND v0 = 'role:class_teacher'
  AND v2 LIKE 'menu:%'
  AND v3 = 'access'
  AND v4 = 'allow'
ORDER BY menu_id;

-- ================================================================
-- 5. 检查是否有无效的角色代码
-- ================================================================

-- 检查 Casbin 策略中引用的角色是否在 roles 表中存在
WITH casbin_roles AS (
    SELECT DISTINCT SUBSTRING(v0 FROM 'role:(.*)') as role_code
    FROM public.casbin_policies
    WHERE ptype = 'p'
      AND v0 LIKE 'role:%'
      AND v2 LIKE 'menu:%'
      AND v3 = 'access'
),
system_roles AS (
    SELECT code as role_code
    FROM public.roles
    WHERE is_active = true
)
SELECT 
    '=== 角色代码验证 ===' as section,
    cr.role_code,
    CASE 
        WHEN sr.role_code IS NOT NULL THEN '✅ 角色存在'
        WHEN cr.role_code = 'menu:access' THEN '⚠️ 无效格式'
        ELSE '❌ 角色不存在'
    END as validation_result
FROM casbin_roles cr
LEFT JOIN system_roles sr ON cr.role_code = sr.role_code
ORDER BY cr.role_code;

-- ================================================================
-- 6. 性能测试
-- ================================================================

-- 测试获取所有菜单角色要求的性能
\timing on

SELECT 
    '=== 性能测试 ===' as section,
    COUNT(*) as total_menus,
    COUNT(CASE WHEN array_length(get_menu_required_roles(menu_id), 1) > 0 THEN 1 END) as menus_with_roles
FROM public.menu_permissions;

\timing off

-- ================================================================
-- 7. 清理测试函数
-- ================================================================

-- 删除测试函数
DROP FUNCTION IF EXISTS get_menu_required_roles(TEXT);

-- ================================================================
-- 8. 总结报告
-- ================================================================

SELECT 
    '=== 实施总结 ===' as section,
    'required_roles字段已从menu_permissions表中移除' as status,
    '角色要求现在完全从casbin_policies表中获取' as implementation,
    '备份数据已保存到menu_permissions_required_roles_backup表' as backup_info;
