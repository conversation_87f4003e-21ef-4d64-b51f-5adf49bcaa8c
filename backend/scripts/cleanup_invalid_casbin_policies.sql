-- 清理无效的 Casbin 策略并补充缺失的角色权限
-- 修复 menu:access 等无效角色代码

-- ================================================================
-- 1. 删除无效的 menu:access 策略
-- ================================================================

-- 查看要删除的无效策略
SELECT 
    '=== 即将删除的无效策略 ===' as section,
    ptype, v0, v1, v2, v3, v4, v5
FROM public.casbin_policies
WHERE ptype = 'p' 
  AND v0 = 'role:menu:access'
  AND v2 LIKE 'menu:%';

-- 删除无效的 menu:access 策略
DELETE FROM public.casbin_policies
WHERE ptype = 'p' 
  AND v0 = 'role:menu:access'
  AND v2 LIKE 'menu:%';

-- ================================================================
-- 2. 根据备份数据补充缺失的 Casbin 策略
-- ================================================================

-- 为 administrative_classes_management 补充缺失的角色
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:principal', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:principal' 
    AND v2 = 'menu:administrative_classes_management' AND v3 = 'access'
);

INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:academic_director', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:academic_director' 
    AND v2 = 'menu:administrative_classes_management' AND v3 = 'access'
);

-- 为 grade_score_management 补充缺失的角色
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:subject_teacher', '*', 'menu:grade_score_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:subject_teacher' 
    AND v2 = 'menu:grade_score_management' AND v3 = 'access'
);

INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:principal', '*', 'menu:grade_score_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:principal' 
    AND v2 = 'menu:grade_score_management' AND v3 = 'access'
);

-- 为 student_management 补充缺失的角色
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:subject_teacher', '*', 'menu:student_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:subject_teacher' 
    AND v2 = 'menu:student_management' AND v3 = 'access'
);

INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
SELECT 'p', 'role:principal', '*', 'menu:student_management', 'access', 'allow', 'menu_access'
WHERE NOT EXISTS (
    SELECT 1 FROM public.casbin_policies 
    WHERE ptype = 'p' AND v0 = 'role:principal' 
    AND v2 = 'menu:student_management' AND v3 = 'access'
);

-- ================================================================
-- 3. 验证修复结果
-- ================================================================

-- 重新创建测试函数
CREATE OR REPLACE FUNCTION get_menu_required_roles(menu_id_param TEXT)
RETURNS TEXT[] AS $$
DECLARE
    roles TEXT[];
BEGIN
    SELECT ARRAY_AGG(SUBSTRING(v0 FROM 'role:(.*)'))
    INTO roles
    FROM public.casbin_policies
    WHERE ptype = 'p'
      AND v2 = 'menu:' || menu_id_param
      AND v3 = 'access'
      AND v4 = 'allow'
      AND v0 LIKE 'role:%';
    
    RETURN COALESCE(roles, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql;

-- 验证修复后的角色要求
SELECT 
    '=== 修复后的菜单角色要求 ===' as section,
    menu_id,
    name,
    get_menu_required_roles(menu_id) as required_roles_from_casbin,
    array_length(get_menu_required_roles(menu_id), 1) as roles_count
FROM public.menu_permissions
WHERE menu_id IN (
    'administrative_classes_management',
    'student_management', 
    'grade_score_management',
    'teaching_aids_management',
    'exam_management'
)
ORDER BY menu_id;

-- 对比修复后的数据
WITH backup_data AS (
    SELECT 
        menu_id,
        required_roles as backup_roles
    FROM public.menu_permissions_required_roles_backup
),
casbin_data AS (
    SELECT 
        menu_id,
        get_menu_required_roles(menu_id) as casbin_roles
    FROM public.menu_permissions
)
SELECT 
    '=== 修复后数据对比 ===' as section,
    COALESCE(b.menu_id, c.menu_id) as menu_id,
    b.backup_roles,
    c.casbin_roles,
    CASE 
        WHEN b.backup_roles IS NULL THEN '仅在Casbin中'
        WHEN c.casbin_roles = ARRAY[]::TEXT[] THEN '仅在备份中'
        WHEN b.backup_roles <@ c.casbin_roles AND c.casbin_roles <@ b.backup_roles THEN '✅ 完全匹配'
        WHEN b.backup_roles <@ c.casbin_roles THEN '✅ Casbin包含备份'
        ELSE '⚠️ 仍有差异'
    END as comparison_result
FROM backup_data b
FULL OUTER JOIN casbin_data c ON b.menu_id = c.menu_id
WHERE COALESCE(b.menu_id, c.menu_id) IN (
    'administrative_classes_management',
    'student_management', 
    'grade_score_management',
    'teaching_aids_management',
    'exam_management'
)
ORDER BY menu_id;

-- 检查是否还有无效的角色代码
SELECT 
    '=== 清理后角色代码验证 ===' as section,
    SUBSTRING(v0 FROM 'role:(.*)') as role_code,
    COUNT(*) as policy_count
FROM public.casbin_policies
WHERE ptype = 'p'
  AND v0 LIKE 'role:%'
  AND v2 LIKE 'menu:%'
  AND v3 = 'access'
  AND SUBSTRING(v0 FROM 'role:(.*)') NOT IN (
    SELECT code FROM public.roles WHERE is_active = true
  )
GROUP BY SUBSTRING(v0 FROM 'role:(.*)')
ORDER BY role_code;

-- 清理测试函数
DROP FUNCTION IF EXISTS get_menu_required_roles(TEXT);

-- ================================================================
-- 4. 统计信息
-- ================================================================

SELECT 
    '=== 清理统计 ===' as section,
    'Casbin菜单策略总数' as metric,
    COUNT(*) as count
FROM public.casbin_policies
WHERE ptype = 'p' 
  AND v2 LIKE 'menu:%' 
  AND v3 = 'access'

UNION ALL

SELECT 
    '=== 清理统计 ===' as section,
    '有效角色的菜单策略数' as metric,
    COUNT(*) as count
FROM public.casbin_policies cp
WHERE ptype = 'p' 
  AND v2 LIKE 'menu:%' 
  AND v3 = 'access'
  AND SUBSTRING(v0 FROM 'role:(.*)') IN (
    SELECT code FROM public.roles WHERE is_active = true
  );
