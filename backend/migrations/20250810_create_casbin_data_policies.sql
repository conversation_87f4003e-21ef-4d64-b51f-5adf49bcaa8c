-- Migration: Create casbin_data_policies table for elegant policy-based data filtering
-- Date: 2025-08-10
-- Description: 创建语义化的数据策略表，支持更优雅的权限过滤机制

-- =============================================
-- 1. 创建数据策略表
-- =============================================

CREATE TABLE IF NOT EXISTS public.casbin_data_policies (
    id BIGSERIAL PRIMARY KEY,
    ptype VARCHAR(10) DEFAULT 'p' NOT NULL,
    subject VARCHAR(100) NOT NULL,           -- 角色名或用户标识
    domain VARCHAR(100) NOT NULL,            -- 租户ID或通配符
    object VARCHAR(100) NOT NULL,            -- 资源类型 (student, teacher, class等)
    action VARCHAR(50) NOT NULL,             -- 操作类型 (read, write, delete等)
    effect VARCHAR(10) DEFAULT 'allow' NOT NULL, -- 效果 (allow, deny)
    scope_type VARCHAR(50) NOT NULL,         -- 范围类型 (school, grade, class, self等)
    scope_value VARCHAR(200),                -- 范围值 (all, managed, only等)
    condition_template TEXT,                 -- SQL条件模板
    priority INTEGER DEFAULT 0 NOT NULL,    -- 优先级 (数值越大优先级越高)
    description TEXT,                        -- 策略描述
    is_active BOOLEAN DEFAULT TRUE,          -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id),
    updated_by UUID REFERENCES public.users(id)
);

-- =============================================
-- 2. 创建索引
-- =============================================

-- 主要查询索引
CREATE INDEX IF NOT EXISTS idx_casbin_data_policies_lookup 
ON public.casbin_data_policies (subject, domain, object, action, effect, is_active);

-- 优先级排序索引
CREATE INDEX IF NOT EXISTS idx_casbin_data_policies_priority 
ON public.casbin_data_policies (priority DESC, id ASC);

-- 范围类型索引
CREATE INDEX IF NOT EXISTS idx_casbin_data_policies_scope 
ON public.casbin_data_policies (scope_type, scope_value);

-- 时间索引
CREATE INDEX IF NOT EXISTS idx_casbin_data_policies_created_at 
ON public.casbin_data_policies (created_at DESC);

-- =============================================
-- 3. 插入默认策略数据
-- =============================================

-- 校长权限：全校数据访问
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('principal', '*', 'student', 'read', 'allow', 'school', 'all', '1=1', 100, '校长可以访问全校所有学生数据'),
('principal', '*', 'teacher', 'read', 'allow', 'school', 'all', '1=1', 100, '校长可以访问全校所有教师数据'),
('principal', '*', 'administrative_class', 'read', 'allow', 'school', 'all', '1=1', 100, '校长可以访问全校所有班级数据');

-- 年级长权限：年级范围数据访问
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('grade_director', '*', 'student', 'read', 'allow', 'grade', 'managed', 
 's.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)', 
 80, '年级长可以访问所管理年级的学生数据'),
('grade_director', '*', 'administrative_class', 'read', 'allow', 'grade', 'managed',
 'ac.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)',
 80, '年级长可以访问所管理年级的班级数据');

-- 班主任权限：班级范围数据访问
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('class_teacher', '*', 'student', 'read', 'allow', 'class', 'managed',
 's.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 70, '班主任可以访问所管理班级的学生数据'),
('class_teacher', '*', 'administrative_class', 'read', 'allow', 'class', 'managed',
 'ac.id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 70, '班主任可以访问所管理的班级数据');

-- 任课老师权限：教学班级范围数据访问
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('subject_teacher', '*', 'student', 'read', 'allow', 'teaching_class', 'assigned',
 's.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id})',
 60, '任课老师可以访问所教授班级的学生数据'),
('subject_teacher', '*', 'administrative_class', 'read', 'allow', 'teaching_class', 'assigned',
 'ac.id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id})',
 60, '任课老师可以访问所教授的班级数据');

-- 学科组长权限：学科相关范围数据访问
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('subject_group_leader', '*', 'student', 'read', 'allow', 'subject_group', 'managed',
 's.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc JOIN "${schema_name}".subject_groups sg ON tc.subject = sg.subject_name WHERE sg.leader_user_id = ${user_id})',
 50, '学科组长可以访问学科组相关班级的学生数据'),
('subject_group_leader', '*', 'teacher', 'read', 'allow', 'subject_group', 'managed',
 't.user_id IN (SELECT DISTINCT tc.teacher_user_id FROM "${schema_name}".teacher_class_subjects tc JOIN "${schema_name}".subject_groups sg ON tc.subject = sg.subject_name WHERE sg.leader_user_id = ${user_id})',
 50, '学科组长可以访问学科组内的教师数据');

-- 学生权限：只能访问自己的数据
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('student', '*', 'student', 'read', 'allow', 'self', 'only',
 's.id = (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' AND target_id IS NOT NULL LIMIT 1)',
 10, '学生只能访问自己的数据');

-- 家长权限：只能访问孩子的数据
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('parent', '*', 'student', 'read', 'allow', 'children', 'related',
 's.id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' AND target_id IS NOT NULL)',
 10, '家长只能访问孩子的数据');

-- =============================================
-- 4. 写权限策略
-- =============================================

-- 校长写权限
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('principal', '*', 'student', 'write', 'allow', 'school', 'all', '1=1', 100, '校长可以修改全校学生数据'),
('principal', '*', 'teacher', 'write', 'allow', 'school', 'all', '1=1', 100, '校长可以修改全校教师数据'),
('principal', '*', 'administrative_class', 'write', 'allow', 'school', 'all', '1=1', 100, '校长可以修改全校班级数据');

-- 年级长写权限
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('grade_director', '*', 'student', 'write', 'allow', 'grade', 'managed',
 's.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)',
 80, '年级长可以修改所管理年级的学生数据');

-- 班主任写权限
INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('class_teacher', '*', 'student', 'write', 'allow', 'class', 'managed',
 's.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 70, '班主任可以修改所管理班级的学生数据');

-- =============================================
-- 5. 特殊策略：系统管理员
-- =============================================

INSERT INTO public.casbin_data_policies 
(subject, domain, object, action, effect, scope_type, scope_value, condition_template, priority, description) VALUES
('system_admin', '*', '*', '*', 'allow', 'system', 'all', '1=1', 1000, '系统管理员拥有所有权限'),
('super_admin', '*', '*', '*', 'allow', 'system', 'all', '1=1', 1000, '超级管理员拥有所有权限');

-- =============================================
-- 6. 创建触发器更新 updated_at
-- =============================================

CREATE OR REPLACE FUNCTION update_casbin_data_policies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_casbin_data_policies_updated_at
    BEFORE UPDATE ON public.casbin_data_policies
    FOR EACH ROW
    EXECUTE FUNCTION update_casbin_data_policies_updated_at();

-- =============================================
-- 7. 添加表注释
-- =============================================

COMMENT ON TABLE public.casbin_data_policies IS '语义化数据策略表，用于优雅的权限过滤机制';
COMMENT ON COLUMN public.casbin_data_policies.subject IS '角色名或用户标识';
COMMENT ON COLUMN public.casbin_data_policies.domain IS '租户ID，支持通配符*';
COMMENT ON COLUMN public.casbin_data_policies.object IS '资源类型，如student、teacher、class等';
COMMENT ON COLUMN public.casbin_data_policies.action IS '操作类型，如read、write、delete等';
COMMENT ON COLUMN public.casbin_data_policies.effect IS '策略效果，allow或deny';
COMMENT ON COLUMN public.casbin_data_policies.scope_type IS '数据范围类型，如school、grade、class、self等';
COMMENT ON COLUMN public.casbin_data_policies.scope_value IS '范围值，如all、managed、only等';
COMMENT ON COLUMN public.casbin_data_policies.condition_template IS 'SQL条件模板，支持变量替换';
COMMENT ON COLUMN public.casbin_data_policies.priority IS '策略优先级，数值越大优先级越高';
COMMENT ON COLUMN public.casbin_data_policies.description IS '策略描述，便于理解和维护';

-- =============================================
-- 8. 创建策略管理视图
-- =============================================

CREATE OR REPLACE VIEW public.v_casbin_data_policies_summary AS
SELECT 
    subject,
    object,
    action,
    scope_type,
    COUNT(*) as policy_count,
    MAX(priority) as max_priority,
    MIN(created_at) as first_created,
    MAX(updated_at) as last_updated
FROM public.casbin_data_policies
WHERE is_active = TRUE
GROUP BY subject, object, action, scope_type
ORDER BY subject, object, action, scope_type;

COMMENT ON VIEW public.v_casbin_data_policies_summary IS '数据策略汇总视图，便于管理和监控';

-- =============================================
-- 9. 创建策略验证函数
-- =============================================

CREATE OR REPLACE FUNCTION validate_casbin_data_policy(
    p_subject VARCHAR(100),
    p_domain VARCHAR(100),
    p_object VARCHAR(100),
    p_action VARCHAR(50)
) RETURNS TABLE (
    policy_id BIGINT,
    scope_type VARCHAR(50),
    condition_template TEXT,
    priority INTEGER,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cdp.id,
        cdp.scope_type,
        cdp.condition_template,
        cdp.priority,
        cdp.description
    FROM public.casbin_data_policies cdp
    WHERE cdp.subject = p_subject
    AND (cdp.domain = p_domain OR cdp.domain = '*')
    AND cdp.object = p_object
    AND cdp.action = p_action
    AND cdp.effect = 'allow'
    AND cdp.is_active = TRUE
    ORDER BY cdp.priority DESC, cdp.id ASC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION validate_casbin_data_policy IS '验证和查询匹配的数据策略';

-- 完成迁移
SELECT 'casbin_data_policies table and related objects created successfully' as result;
