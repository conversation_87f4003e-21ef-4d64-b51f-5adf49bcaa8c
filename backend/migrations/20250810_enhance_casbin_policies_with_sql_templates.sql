-- Migration: Enhance existing casbin_policies with SQL templates in v5 field
-- Date: 2025-08-10
-- Description: 在现有casbin_policies表的v5字段中添加SQL条件模板，实现优雅的策略驱动过滤

-- =============================================
-- 1. 为现有策略添加SQL模板
-- =============================================

-- 校长权限：全校数据访问（无限制）
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:principal', '*', 'student:school', 'read', 'allow', '1=1', 'template'),
('p', 'role:principal', '*', 'student:*', 'read', 'allow', '1=1', 'template'),
('p', 'role:principal', '*', 'administrative_class:school', 'read', 'allow', '1=1', 'template'),
('p', 'role:principal', '*', 'teacher:school', 'read', 'allow', '1=1', 'template')
ON CONFLICT DO NOTHING;

-- 年级长权限：年级范围数据访问
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:grade_director', '*', 'student:grade', 'read', 'allow', 
 's.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)', 
 'template'),
('p', 'role:grade_director', '*', 'administrative_class:grade', 'read', 'allow',
 'ac.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)',
 'template')
ON CONFLICT DO NOTHING;

-- 班主任权限：班级范围数据访问
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:class_teacher', '*', 'student:class', 'read', 'allow',
 's.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 'template'),
('p', 'role:class_teacher', '*', 'administrative_class:class', 'read', 'allow',
 'ac.id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 'template')
ON CONFLICT DO NOTHING;

-- 任课老师权限：教学班级范围数据访问
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:subject_teacher', '*', 'student:teaching_class', 'read', 'allow',
 's.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id})',
 'template'),
('p', 'role:subject_teacher', '*', 'administrative_class:teaching_class', 'read', 'allow',
 'ac.id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id})',
 'template')
ON CONFLICT DO NOTHING;

-- 学科组长权限：学科组相关范围数据访问
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:subject_group_leader', '*', 'student:subject_group', 'read', 'allow',
 's.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc JOIN "${schema_name}".subject_groups sg ON tc.subject = sg.subject_name WHERE sg.leader_user_id = ${user_id})',
 'template'),
('p', 'role:subject_group_leader', '*', 'teacher:subject_group', 'read', 'allow',
 't.user_id IN (SELECT DISTINCT tc.teacher_user_id FROM "${schema_name}".teacher_class_subjects tc JOIN "${schema_name}".subject_groups sg ON tc.subject = sg.subject_name WHERE sg.leader_user_id = ${user_id})',
 'template')
ON CONFLICT DO NOTHING;

-- 学生权限：只能访问自己的数据
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:student', '*', 'student:self', 'read', 'allow',
 's.id = (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' AND target_id IS NOT NULL LIMIT 1)',
 'template')
ON CONFLICT DO NOTHING;

-- 家长权限：只能访问孩子的数据
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:parent', '*', 'student:children', 'read', 'allow',
 's.id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' AND target_id IS NOT NULL)',
 'template')
ON CONFLICT DO NOTHING;

-- =============================================
-- 2. 写权限策略（带SQL模板）
-- =============================================

-- 校长写权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:principal', '*', 'student:school', 'write', 'allow', '1=1', 'template'),
('p', 'role:principal', '*', 'administrative_class:school', 'write', 'allow', '1=1', 'template'),
('p', 'role:principal', '*', 'teacher:school', 'write', 'allow', '1=1', 'template')
ON CONFLICT DO NOTHING;

-- 年级长写权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:grade_director', '*', 'student:grade', 'write', 'allow',
 's.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'' AND target_id IS NOT NULL)',
 'template')
ON CONFLICT DO NOTHING;

-- 班主任写权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:class_teacher', '*', 'student:class', 'write', 'allow',
 's.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'' AND target_id IS NOT NULL)',
 'template')
ON CONFLICT DO NOTHING;

-- =============================================
-- 3. 特殊策略：系统管理员
-- =============================================

INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:system_admin', '*', '*', '*', 'allow', '1=1', 'template'),
('p', 'role:super_admin', '*', '*', '*', 'allow', '1=1', 'template')
ON CONFLICT DO NOTHING;

-- =============================================
-- 4. 高级SQL模板示例（使用变量替换）
-- =============================================

-- 班主任可以访问所管理班级的学生，以及这些学生的成绩
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:class_teacher', '*', 'grade:class_students', 'read', 'allow',
 'g.student_id IN (SELECT s.id FROM "${schema_name}".students s WHERE s.administrative_class_id IN (${user.managed_classes}))',
 'template')
ON CONFLICT DO NOTHING;

-- 任课老师可以访问所教授班级学生的特定学科成绩
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:subject_teacher', '*', 'grade:subject_specific', 'read', 'allow',
 'g.student_id IN (SELECT s.id FROM "${schema_name}".students s WHERE s.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id})) AND g.subject = (SELECT DISTINCT tc.subject FROM "${schema_name}".teacher_class_subjects tc WHERE tc.teacher_user_id = ${user_id} LIMIT 1)',
 'template')
ON CONFLICT DO NOTHING;

-- =============================================
-- 5. 创建策略查询优化索引
-- =============================================

-- 为策略查询创建复合索引
CREATE INDEX IF NOT EXISTS idx_casbin_policies_enhanced_lookup 
ON public.casbin_policies (ptype, v0, v1, v2, v3, v4) 
WHERE ptype = 'p' AND v4 = 'allow';

-- 为v5字段创建索引（用于SQL模板查询）
CREATE INDEX IF NOT EXISTS idx_casbin_policies_v5_template 
ON public.casbin_policies (v5) 
WHERE v5 IS NOT NULL AND v5 != '';

-- 为租户隔离创建索引
CREATE INDEX IF NOT EXISTS idx_casbin_policies_tenant_lookup 
ON public.casbin_policies (tenant_id, ptype, v0, v2);

-- =============================================
-- 6. 创建策略验证函数
-- =============================================

CREATE OR REPLACE FUNCTION get_enhanced_casbin_policies(
    p_roles TEXT[],
    p_tenant_id VARCHAR(100),
    p_resource VARCHAR(100),
    p_action VARCHAR(50)
) RETURNS TABLE (
    policy_id BIGINT,
    subject VARCHAR(256),
    object VARCHAR(256),
    action VARCHAR(256),
    effect VARCHAR(256),
    sql_template TEXT,
    has_template BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.v0,
        cp.v2,
        cp.v3,
        cp.v4,
        cp.v5,
        (cp.v5 IS NOT NULL AND cp.v5 != '') as has_template
    FROM public.casbin_policies cp
    WHERE cp.ptype = 'p'
    AND cp.v0 = ANY(p_roles)
    AND (cp.v1 = p_tenant_id OR cp.v1 = '*')
    AND (cp.v2 = p_resource || ':*' OR cp.v2 LIKE p_resource || ':%' OR cp.v2 = '*')
    AND (cp.v3 = p_action OR cp.v3 = '*')
    AND cp.v4 = 'allow'
    ORDER BY 
        CASE WHEN cp.v5 IS NOT NULL AND cp.v5 != '' THEN 1 ELSE 2 END,  -- 有SQL模板的优先
        CASE WHEN cp.v2 = p_resource || ':*' THEN 1 
             WHEN cp.v2 LIKE p_resource || ':%' THEN 2 
             ELSE 3 END,  -- 精确匹配优先
        cp.created_at DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION get_enhanced_casbin_policies IS '获取增强的Casbin策略，优先返回包含SQL模板的策略';

-- =============================================
-- 7. 创建策略模板验证函数
-- =============================================

CREATE OR REPLACE FUNCTION validate_sql_template(template TEXT) 
RETURNS BOOLEAN AS $$
BEGIN
    -- 基本的SQL模板验证
    IF template IS NULL OR trim(template) = '' THEN
        RETURN FALSE;
    END IF;
    
    -- 检查是否包含危险的SQL关键字
    IF upper(template) ~ '(DROP|DELETE|TRUNCATE|ALTER|CREATE|INSERT|UPDATE)\s' THEN
        RETURN FALSE;
    END IF;
    
    -- 检查是否包含有效的变量占位符
    IF template ~ '\$\{[a-zA-Z_][a-zA-Z0-9_.]*\}' THEN
        RETURN TRUE;
    END IF;
    
    -- 检查是否为简单的条件表达式
    IF template ~ '^[a-zA-Z0-9_.\s=<>!()''",\-\*]+$' THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION validate_sql_template IS '验证SQL模板的安全性和有效性';

-- =============================================
-- 8. 更新现有策略的注释
-- =============================================

COMMENT ON COLUMN public.casbin_policies.v5 IS '扩展字段：可存储SQL条件模板、元数据或其他策略相关信息';

-- =============================================
-- 9. 创建策略管理视图
-- =============================================

CREATE OR REPLACE VIEW public.v_enhanced_casbin_policies AS
SELECT 
    id,
    ptype,
    v0 as subject,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect,
    v5 as sql_template,
    tenant_id,
    CASE WHEN v5 IS NOT NULL AND v5 != '' THEN 'template' ELSE 'standard' END as policy_type,
    validate_sql_template(v5) as template_valid,
    created_at,
    updated_at
FROM public.casbin_policies
WHERE ptype = 'p'
ORDER BY 
    CASE WHEN v5 IS NOT NULL AND v5 != '' THEN 1 ELSE 2 END,
    v0, v2, v3;

COMMENT ON VIEW public.v_enhanced_casbin_policies IS '增强的Casbin策略视图，显示策略类型和模板有效性';

-- =============================================
-- 10. 插入策略使用示例数据
-- =============================================

-- 为测试租户添加一些示例策略
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
-- 测试班主任策略
('p', 'role:class_teacher', 'test_tenant', 'student:class', 'read', 'allow',
 's.administrative_class_id IN (SELECT target_id FROM "test_tenant".user_identities WHERE user_id = ${user_id} AND target_type = ''class'')',
 'test_tenant'),
-- 测试学生策略
('p', 'role:student', 'test_tenant', 'student:self', 'read', 'allow',
 's.id = (SELECT target_id FROM "test_tenant".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' LIMIT 1)',
 'test_tenant')
ON CONFLICT DO NOTHING;

-- 完成迁移
SELECT 'Enhanced casbin_policies with SQL templates created successfully' as result;

-- 显示策略统计
SELECT 
    'Policy Statistics' as info,
    COUNT(*) as total_policies,
    COUNT(CASE WHEN v5 IS NOT NULL AND v5 != '' THEN 1 END) as policies_with_templates,
    COUNT(CASE WHEN validate_sql_template(v5) THEN 1 END) as valid_templates
FROM public.casbin_policies 
WHERE ptype = 'p';
