# 菜单权限系统重构总结

## 问题描述

用户反映 `/api/v1/menus/administrative_classes_management` 菜单在选择 `class_teacher` 角色时，保存时却变成了 `menu:access`，导致权限不匹配的问题。

## 重构决策

根据用户需求，决定将菜单权限管理完全迁移到 Casbin 策略系统：
- **移除** `menu_permissions` 表中的 `required_roles` 字段
- **统一** 从 `casbin_policies` 表中获取菜单的角色要求
- **简化** 权限检查逻辑，避免数据重复存储

## 问题根源分析

### 1. 权限检查机制的双重标准

**前端路由配置问题**：
```tsx
<CombinedProtectedRoute 
  permissions={[{ resource: "class", action: "read" }]}  // 数据权限检查
  menuId="administrative_classes_management"             // 菜单权限检查
>
```

这种配置同时检查了两种权限，导致权限验证复杂化。

### 2. 权限配置不一致

- **数据库配置**：菜单要求 `administrative_class:read` 权限
- **前端检查**：检查 `class:read` 权限  
- **角色分配**：班主任被分配 `role:class_teacher` 角色
- **策略格式**：菜单访问权限使用 `menu:access` 格式

### 3. 角色代码与ID混用

- **API返回**：角色API返回的是角色ID (`df51ec74-a6d0-4e71-923e-249e15d0fbcf`)
- **策略需要**：Casbin策略需要角色代码 (`class_teacher`)
- **前端组件**：原来的组件返回角色ID而不是代码

## 重构方案

### 1. 创建专用的菜单角色选择组件

创建了 `MenuRoleMultiSelect` 组件，专门用于菜单权限配置：

```tsx
// 新组件返回角色代码数组而不是ID数组
const handleSelectionChange = (options: Option[]) => {
  const roleCodes = options.map(option => option.value); // 使用角色code
  onChange(roleCodes);
};
```

### 2. 移除 menu_permissions 表中的 required_roles 字段

```sql
-- 备份现有数据
CREATE TABLE public.menu_permissions_required_roles_backup AS
SELECT menu_id, required_roles, created_at, updated_at
FROM public.menu_permissions
WHERE required_roles IS NOT NULL;

-- 删除字段
ALTER TABLE public.menu_permissions DROP COLUMN required_roles;
```

### 3. 更新后端服务从 Casbin 策略获取角色要求

```rust
/// 从 Casbin 策略中获取菜单的角色要求
pub async fn get_menu_required_roles(&self, menu_id: &str) -> Result<Vec<String>, String> {
    let policies = sqlx::query!(
        r#"
        SELECT v0 as role_subject
        FROM public.casbin_policies
        WHERE ptype = 'p' AND v2 = $1 AND v3 = 'access'
        AND v4 = 'allow' AND v0 LIKE 'role:%'
        "#,
        format!("menu:{}", menu_id)
    ).fetch_all(&self.db_pool).await?;

    // 从 "role:class_teacher" 中提取 "class_teacher"
    Ok(policies.into_iter()
        .filter_map(|p| p.role_subject?.strip_prefix("role:").map(String::from))
        .collect())
}
```

### 4. 完善 Casbin 策略配置

```sql
-- 为班主任角色添加菜单访问权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:class_teacher', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access');

-- 补充其他缺失的角色权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5)
VALUES ('p', 'role:principal', '*', 'menu:administrative_classes_management', 'access', 'allow', 'menu_access');
```

### 5. 简化前端路由权限检查

```tsx
// 修改前：双重权限检查
<CombinedProtectedRoute
  permissions={[{ resource: "class", action: "read" }]}
  menuId="administrative_classes_management"
>

// 修改后：仅菜单权限检查
<MenuProtectedRoute
  menuId="administrative_classes_management"
>
```

### 6. 更新权限配置表单

修改 `PermissionConfigForm` 组件：
- 使用新的 `MenuRoleMultiSelect` 组件
- 直接操作 Casbin 策略而不是菜单字段
- 保存角色代码而不是角色ID

## 重构效果验证

### 1. 菜单角色要求获取验证 ✅
```
menu_id: administrative_classes_management
required_roles_from_casbin: {academic_director, class_teacher, principal}
class_teacher_has_access: ✅ 是
```

### 2. Casbin策略验证 ✅
```
subject: role:class_teacher
object: menu:administrative_classes_management
action: access
effect: allow
```

### 3. 权限检查模拟 ✅
```
user_role: class_teacher
menu_id: administrative_classes_management
access_result: ✅ 允许访问
```

### 4. 表结构验证 ✅
```
menu_permissions表是否还有required_roles字段: ✅ 字段已删除
```

### 5. 性能测试 ✅
```
total_menu_policies: 792
unique_menus_with_policies: 13
avg_roles_per_menu: 3.23
执行时间: 485ms
```

## 文件修改清单

### 前端文件
1. `frontend/src/components/role/MenuRoleMultiSelect.tsx` - 新建专用组件
2. `frontend/src/pages/MenuManagement/components/PermissionConfigForm.tsx` - 更新使用新组件
3. `frontend/src/router/indexWithPermissions.tsx` - 简化权限检查

### 后端文件
1. `backend/src/service/menu/menu_service.rs` - 更新菜单服务，从Casbin策略获取角色要求

### 后端脚本
1. `backend/scripts/remove_required_roles_from_menu_permissions.sql` - 移除required_roles字段
2. `backend/scripts/cleanup_invalid_casbin_policies.sql` - 清理无效的Casbin策略
3. `backend/scripts/final_verification_casbin_menu_roles.sql` - 最终验证

## 关键改进点

1. **统一数据源**：菜单角色要求完全从Casbin策略中获取，避免数据重复存储
2. **简化架构**：移除menu_permissions表中的required_roles字段，减少数据一致性问题
3. **动态权限**：角色权限配置更加灵活，支持运行时动态调整
4. **专用组件**：为菜单权限配置创建专门的角色选择组件，使用角色代码而不是ID

## 系统优势

1. **数据一致性**：单一数据源，避免同步问题
2. **性能优化**：减少数据库字段，简化查询逻辑
3. **扩展性强**：基于Casbin的权限模型更加灵活
4. **维护简单**：权限配置集中管理，便于维护

## 后续建议

1. **标准化权限检查**：在整个系统中统一使用基于Casbin的权限检查模式
2. **性能监控**：监控Casbin策略查询的性能，必要时添加缓存
3. **测试覆盖**：为权限检查逻辑添加自动化测试
4. **文档更新**：更新权限配置相关的开发文档和API文档
