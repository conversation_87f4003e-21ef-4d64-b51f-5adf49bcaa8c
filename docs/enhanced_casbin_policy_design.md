# 基于现有Casbin表的优雅策略设计

## 🎯 设计理念

基于您现有的 `casbin_policies` 表结构，充分利用 `v5` 扩展字段存储SQL条件模板，实现优雅的策略驱动数据过滤，无需创建新表。

## 📋 表结构利用

### 现有casbin_policies表结构
```sql
CREATE TABLE public.casbin_policies (
    id BIGSERIAL PRIMARY KEY,
    ptype VARCHAR(100) NOT NULL,      -- 策略类型: p, g, g2
    v0 VARCHAR(256),                  -- 主体 (subject): role:角色名
    v1 VARCHAR(256),                  -- 域 (domain): 租户ID或*
    v2 VARCHAR(256),                  -- 对象 (object): resource:scope
    v3 VARCHAR(256),                  -- 动作 (action): read, write等
    v4 VARCHAR(256),                  -- 效果 (effect): allow/deny
    v5 VARCHAR(256),                  -- 扩展字段: SQL条件模板 ⭐
    tenant_id VARCHAR(100),           -- 租户标识
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### v5字段的创新使用
- **SQL模板存储**：直接在v5字段中存储SQL WHERE条件模板
- **变量替换**：支持 `${user_id}`, `${schema_name}` 等变量
- **优先级机制**：有SQL模板的策略优先级更高

## 🚀 策略示例

### 1. 班主任策略
```sql
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:class_teacher', '*', 'student:class', 'read', 'allow',
 's.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''class'')',
 'template');
```

### 2. 年级长策略
```sql
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:grade_director', '*', 'student:grade', 'read', 'allow',
 's.grade_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''grade'')',
 'template');
```

### 3. 学生策略
```sql
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:student', '*', 'student:self', 'read', 'allow',
 's.id = (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id} AND target_type = ''student'' LIMIT 1)',
 'template');
```

### 4. 校长策略
```sql
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:principal', '*', 'student:school', 'read', 'allow', '1=1', 'template');
```

## 🔧 过滤器实现

### EnhancedCasbinDataFilter核心逻辑
```rust
impl EnhancedCasbinDataFilter {
    async fn get_filter_from_casbin_policies(&self, context: &FilterContext) -> Result<Option<FilterCondition>> {
        // 1. 获取用户角色
        let user_roles = self.get_user_roles(context).await?;
        
        // 2. 查询匹配的策略（优先选择有SQL模板的）
        let policies = self.query_enhanced_policies(&user_roles, &context.tenant_id, &context.resource, &context.action).await?;
        
        // 3. 选择最佳策略
        let selected_policy = self.select_best_policy(&policies)?;
        
        // 4. 生成过滤条件
        if let Some(v5) = &selected_policy.v5 {
            if self.is_sql_template(v5) {
                // 渲染SQL模板
                let sql_condition = self.render_sql_template(v5, context).await?;
                return Ok(Some(FilterCondition { sql_condition, bind_params: vec![] }));
            }
        }
        
        // 5. 回退到基于v2对象字段的条件生成
        self.generate_condition_from_object(&selected_policy.v2, context).await
    }
}
```

### 策略查询优化
```sql
-- 优先选择有SQL模板的策略
SELECT * FROM casbin_policies
WHERE ptype = 'p'
AND v0 IN ('role:class_teacher', 'role:student', ...)
AND (v1 = 'tenant_001' OR v1 = '*')
AND (v2 = 'student:*' OR v2 LIKE 'student:%' OR v2 = '*')
AND v3 = 'read'
AND v4 = 'allow'
ORDER BY 
    CASE WHEN v5 IS NOT NULL AND v5 != '' THEN 1 ELSE 2 END,  -- 有SQL模板的优先
    CASE WHEN v2 = 'student:*' THEN 1 ELSE 2 END,             -- 精确匹配优先
    created_at DESC;
```

## 🎨 变量替换系统

### 基本变量
- `${user_id}`: 当前用户ID
- `${tenant_id}`: 租户ID
- `${schema_name}`: 数据库schema名称

### 高级变量
- `${user.managed_classes}`: 用户管理的班级ID列表
- `${user.managed_grades}`: 用户管理的年级ID列表
- `${user.student_id}`: 用户对应的学生ID

### 变量替换示例
```rust
// 模板: s.administrative_class_id IN (${user.managed_classes})
// 替换后: s.administrative_class_id IN ('class_001', 'class_002', 'class_003')

async fn process_advanced_template_variables(&self, mut template: String, context: &FilterContext) -> Result<String> {
    if template.contains("${user.managed_classes}") {
        let managed_classes = self.get_user_managed_classes(context).await?;
        let classes_str = managed_classes.iter()
            .map(|id| format!("'{}'", id))
            .collect::<Vec<_>>()
            .join(",");
        template = template.replace("${user.managed_classes}", &classes_str);
    }
    Ok(template)
}
```

## 📊 优势对比

| 方面 | 传统硬编码 | 增强Casbin策略 |
|------|-----------|---------------|
| **策略存储** | 代码中硬编码 | 数据库中存储 |
| **SQL条件** | 程序生成 | 模板渲染 |
| **维护方式** | 修改代码部署 | 修改数据库即时生效 |
| **扩展性** | 需要开发 | 配置策略即可 |
| **复杂度** | 高（200+行代码） | 低（50行核心逻辑） |
| **测试性** | 需要单元测试 | 可直接测试SQL |
| **表结构** | 无需改动 | 充分利用现有结构 |

## 🔍 策略管理

### 策略查询函数
```sql
-- 获取增强的Casbin策略
SELECT * FROM get_enhanced_casbin_policies(
    ARRAY['role:class_teacher', 'role:student'],  -- 用户角色
    'tenant_001',                                 -- 租户ID
    'student',                                    -- 资源类型
    'read'                                        -- 操作类型
);
```

### 策略验证函数
```sql
-- 验证SQL模板安全性
SELECT validate_sql_template('s.administrative_class_id IN (SELECT target_id FROM "${schema_name}".user_identities WHERE user_id = ${user_id})');
-- 返回: true
```

### 策略管理视图
```sql
-- 查看所有增强策略
SELECT * FROM v_enhanced_casbin_policies 
WHERE policy_type = 'template' 
ORDER BY subject, object;
```

## 🚀 实际应用

### 1. 当前集成状态
- ✅ `EnhancedCasbinDataFilter` 已实现
- ✅ SQL模板策略已配置
- ✅ `StudentService` 已集成增强过滤器
- ✅ 变量替换系统已完成

### 2. 使用方式
```rust
// 在main.rs中
let student_service = StudentServiceFactory::create_with_enhanced_casbin_filter(pool.clone());

// 调用时自动应用策略过滤
let (students, total) = student_service.page_all_student(
    &schema_name, &params, Some(user_id), Some(tenant_id), Some(casbin_service.as_ref())
).await?;
```

### 3. 策略配置示例
```sql
-- 添加新的教研组长策略
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id) VALUES
('p', 'role:research_group_leader', '*', 'student:research_group', 'read', 'allow',
 's.administrative_class_id IN (SELECT class_id FROM research_group_classes WHERE group_leader_id = ${user_id})',
 'template');
```

## 🔧 开发工具

### 策略测试工具
```rust
// 测试特定用户的策略
let filter = EnhancedCasbinDataFilter::new(pool);
let condition = filter.get_filter_condition(&context, casbin_service).await?;
println!("Generated SQL: {}", condition.sql_condition);
```

### 策略管理API
```rust
// 获取策略统计
GET /api/admin/policies/statistics

// 测试用户策略
POST /api/admin/policies/test
{
    "user_id": "uuid",
    "resource": "student",
    "action": "read"
}
```

## 🎯 最佳实践

### 1. 策略设计原则
- **安全优先**：默认拒绝，明确允许
- **最小权限**：只授予必要的权限
- **模板复用**：相似权限使用相同模板

### 2. SQL模板规范
- **变量命名**：使用清晰的变量名
- **安全检查**：避免SQL注入风险
- **性能优化**：使用适当的索引

### 3. 策略维护
- **版本控制**：记录策略变更历史
- **测试验证**：新策略必须经过测试
- **文档更新**：及时更新策略文档

## 🎉 总结

通过充分利用现有 `casbin_policies` 表的 `v5` 字段，我们实现了：

1. **无需新表**：完全基于现有表结构
2. **策略驱动**：SQL条件由策略配置决定
3. **即时生效**：策略修改立即生效
4. **高度灵活**：支持复杂的权限场景
5. **易于维护**：可视化管理和测试

这种设计让casbin策略承担了更多责任，使程序实现更加优雅，同时保持了与现有系统的完全兼容！
