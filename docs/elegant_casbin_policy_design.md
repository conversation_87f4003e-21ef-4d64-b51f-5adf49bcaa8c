# 优雅的Casbin策略设计

## 🎯 设计目标

让casbin策略承担更多的数据范围定义责任，使程序中的过滤器逻辑更加简洁和通用。

## 📋 当前问题分析

### 现有设计的局限性：
1. **硬编码逻辑**：身份到数据范围的映射硬编码在程序中
2. **维护困难**：新增身份类型需要修改代码
3. **策略单一**：casbin只存储通配符权限，缺乏细粒度控制
4. **扩展性差**：难以支持复杂的权限组合

## 🚀 优雅的策略设计方案

### 方案1：基于数据范围的策略设计

#### 策略结构
```
ptype | subject | domain | object | action | effect | data_scope
------|---------|--------|--------|--------|--------|------------
p     | role    | tenant | resource | action | allow | scope_definition
```

#### 具体示例
```sql
-- 校长：全校数据访问权限
('p', 'principal', 'tenant_001', 'student', 'read', 'allow', 'school:*'),

-- 年级长：特定年级数据访问权限  
('p', 'grade_director', 'tenant_001', 'student', 'read', 'allow', 'grade:${user.grade_id}'),

-- 班主任：特定班级数据访问权限
('p', 'class_teacher', 'tenant_001', 'student', 'read', 'allow', 'class:${user.class_id}'),

-- 任课老师：教授班级数据访问权限
('p', 'subject_teacher', 'tenant_001', 'student', 'read', 'allow', 'teaching_classes:${user.user_id}'),

-- 学生：自己的数据访问权限
('p', 'student', 'tenant_001', 'student', 'read', 'allow', 'self:${user.user_id}'),

-- 家长：孩子的数据访问权限
('p', 'parent', 'tenant_001', 'student', 'read', 'allow', 'children:${user.user_id}');
```

### 方案2：分层权限策略设计

#### 策略层次结构
```
1. 基础权限层：定义资源访问权限
2. 数据范围层：定义数据访问范围
3. 条件约束层：定义动态约束条件
```

#### 实现示例
```sql
-- 基础权限层
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4, v5) VALUES
-- 角色基础权限
('p', 'principal', 'tenant_001', 'student', 'read', 'allow', 'base'),
('p', 'class_teacher', 'tenant_001', 'student', 'read', 'allow', 'base'),
('p', 'student', 'tenant_001', 'student', 'read', 'allow', 'base'),

-- 数据范围层
('p', 'principal', 'tenant_001', 'student:scope', 'read', 'allow', 'school:all'),
('p', 'class_teacher', 'tenant_001', 'student:scope', 'read', 'allow', 'class:managed'),
('p', 'student', 'tenant_001', 'student:scope', 'read', 'allow', 'self:only'),

-- 条件约束层
('p', 'class_teacher', 'tenant_001', 'student:condition', 'read', 'allow', 'administrative_class_id IN (SELECT class_id FROM teacher_classes WHERE teacher_user_id = ${user_id})'),
('p', 'student', 'tenant_001', 'student:condition', 'read', 'allow', 'id = (SELECT student_id FROM user_identities WHERE user_id = ${user_id} AND target_type = "student")');
```

### 方案3：语义化策略设计（推荐）

#### 核心思想
使用语义化的策略定义，让策略本身就能表达数据访问逻辑。

#### 策略格式
```
subject | domain | object | action | effect | scope_type | scope_value | condition_template
```

#### 具体实现
```sql
CREATE TABLE casbin_data_policies (
    id BIGSERIAL PRIMARY KEY,
    ptype VARCHAR(10) DEFAULT 'p',
    subject VARCHAR(100) NOT NULL,        -- 角色或用户
    domain VARCHAR(100) NOT NULL,         -- 租户
    object VARCHAR(100) NOT NULL,         -- 资源类型
    action VARCHAR(50) NOT NULL,          -- 操作类型
    effect VARCHAR(10) DEFAULT 'allow',   -- 效果
    scope_type VARCHAR(50) NOT NULL,      -- 范围类型
    scope_value VARCHAR(200),             -- 范围值
    condition_template TEXT,              -- SQL条件模板
    priority INTEGER DEFAULT 0,          -- 优先级
    created_at TIMESTAMP DEFAULT NOW()
);

-- 插入语义化策略
INSERT INTO casbin_data_policies VALUES
-- 校长：全校范围
(1, 'p', 'principal', 'tenant_001', 'student', 'read', 'allow', 'school', 'all', '1=1', 100),

-- 年级长：年级范围
(2, 'p', 'grade_director', 'tenant_001', 'student', 'read', 'allow', 'grade', 'managed', 
 's.grade_id IN (SELECT target_id FROM user_identities WHERE user_id = ${user_id} AND target_type = ''grade'')', 80),

-- 班主任：班级范围
(3, 'p', 'class_teacher', 'tenant_001', 'student', 'read', 'allow', 'class', 'managed',
 's.administrative_class_id IN (SELECT target_id FROM user_identities WHERE user_id = ${user_id} AND target_type = ''class'')', 70),

-- 任课老师：教学班级范围
(4, 'p', 'subject_teacher', 'tenant_001', 'student', 'read', 'allow', 'teaching_class', 'assigned',
 's.administrative_class_id IN (SELECT class_id FROM teacher_class_subjects WHERE teacher_user_id = ${user_id})', 60),

-- 学科组长：学科相关范围
(5, 'p', 'subject_group_leader', 'tenant_001', 'student', 'read', 'allow', 'subject_group', 'managed',
 's.administrative_class_id IN (SELECT DISTINCT tc.class_id FROM teacher_class_subjects tc JOIN subject_groups sg ON tc.subject = sg.subject_name WHERE sg.leader_user_id = ${user_id})', 50),

-- 学生：自己
(6, 'p', 'student', 'tenant_001', 'student', 'read', 'allow', 'self', 'only',
 's.id = (SELECT target_id FROM user_identities WHERE user_id = ${user_id} AND target_type = ''student'')', 10),

-- 家长：孩子
(7, 'p', 'parent', 'tenant_001', 'student', 'read', 'allow', 'children', 'related',
 's.id IN (SELECT target_id FROM user_identities WHERE user_id = ${user_id} AND target_type = ''student'')', 10);
```

## 🔧 优雅的过滤器实现

基于语义化策略的过滤器实现：

```rust
/// 基于策略的数据过滤器（优雅版本）
pub struct PolicyBasedDataFilter {
    pool: PgPool,
}

impl PolicyBasedDataFilter {
    /// 从策略中获取数据过滤条件
    async fn get_filter_from_policies(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 1. 获取用户角色
        let user_roles = self.get_user_roles(context).await?;
        
        // 2. 查询匹配的数据策略
        let policies = self.query_data_policies(
            &user_roles,
            &context.tenant_id,
            &context.resource,
            &context.action,
        ).await?;
        
        // 3. 按优先级排序，选择最高优先级的策略
        if let Some(policy) = policies.first() {
            // 4. 渲染SQL条件模板
            let sql_condition = self.render_condition_template(
                &policy.condition_template,
                context,
            ).await?;
            
            return Ok(Some(FilterCondition {
                sql_condition,
                bind_params: vec![], // 参数已经在模板中处理
            }));
        }
        
        // 5. 默认拒绝访问
        Ok(Some(FilterCondition {
            sql_condition: "1=0".to_string(),
            bind_params: vec![],
        }))
    }
    
    /// 渲染SQL条件模板
    async fn render_condition_template(
        &self,
        template: &str,
        context: &FilterContext,
    ) -> Result<String> {
        let mut rendered = template.to_string();
        
        // 替换变量
        rendered = rendered.replace("${user_id}", &format!("'{}'", context.user_id));
        rendered = rendered.replace("${tenant_id}", &format!("'{}'", context.tenant_id));
        
        Ok(rendered)
    }
    
    /// 查询数据策略
    async fn query_data_policies(
        &self,
        user_roles: &[String],
        tenant_id: &str,
        resource: &str,
        action: &str,
    ) -> Result<Vec<DataPolicy>> {
        let roles_placeholder = user_roles.iter()
            .map(|_| "?")
            .collect::<Vec<_>>()
            .join(",");
            
        let query = format!(
            r#"
            SELECT * FROM casbin_data_policies
            WHERE subject IN ({})
            AND domain = ?
            AND object = ?
            AND action = ?
            AND effect = 'allow'
            ORDER BY priority DESC
            "#,
            roles_placeholder
        );
        
        let mut query_builder = sqlx::query_as::<_, DataPolicy>(&query);
        
        // 绑定角色参数
        for role in user_roles {
            query_builder = query_builder.bind(role);
        }
        
        // 绑定其他参数
        query_builder = query_builder
            .bind(tenant_id)
            .bind(resource)
            .bind(action);
            
        let policies = query_builder.fetch_all(&self.pool).await?;
        Ok(policies)
    }
}
```

## 🎨 策略管理界面设计

### 管理员界面功能
1. **可视化策略编辑**：通过界面配置数据访问范围
2. **模板管理**：预定义常用的SQL条件模板
3. **策略测试**：实时测试策略效果
4. **权限预览**：预览用户的数据访问范围

### 策略配置示例
```json
{
  "role": "class_teacher",
  "resource": "student",
  "scope": {
    "type": "class",
    "value": "managed",
    "description": "班主任只能访问所管理班级的学生"
  },
  "condition": {
    "template": "s.administrative_class_id IN (SELECT target_id FROM user_identities WHERE user_id = ${user_id} AND target_type = 'class')",
    "description": "通过用户身份表查找管理的班级"
  }
}
```

## 📊 优势对比

| 方面 | 当前实现 | 优雅策略设计 |
|------|----------|-------------|
| **维护性** | 需要修改代码 | 只需修改策略配置 |
| **灵活性** | 硬编码逻辑 | 动态策略配置 |
| **可读性** | 代码中的复杂逻辑 | 语义化的策略描述 |
| **测试性** | 需要单元测试 | 可以直接测试策略 |
| **扩展性** | 需要开发新功能 | 配置新策略即可 |
| **性能** | 多次数据库查询 | 一次策略查询 |

## 🔮 实施建议

### 阶段1：策略表设计
1. 创建 `casbin_data_policies` 表
2. 迁移现有策略到新表结构
3. 建立策略优先级体系

### 阶段2：过滤器重构
1. 实现 `PolicyBasedDataFilter`
2. 支持SQL模板渲染
3. 添加策略缓存机制

### 阶段3：管理界面
1. 开发策略管理界面
2. 实现策略测试工具
3. 添加权限预览功能

### 阶段4：优化和监控
1. 性能优化和缓存
2. 策略执行监控
3. 安全审计日志

这种设计让casbin策略承担更多责任，使程序逻辑更加简洁和优雅！
