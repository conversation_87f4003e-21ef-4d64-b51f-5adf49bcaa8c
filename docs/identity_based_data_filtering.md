# 基于身份的数据过滤方案

## 概述

当casbin_policies表中只定义通配符权限策略时，我们通过用户身份(user_identities)来推导具体的数据访问范围。这种方案的核心思想是：

1. **权限策略简化**：casbin中只需要定义通配符权限，如 `student:*`
2. **身份驱动过滤**：根据用户的具体身份（角色+目标范围）来确定数据访问边界
3. **动态范围计算**：基于身份信息动态生成SQL过滤条件

## 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    数据访问请求                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              FilterContext                                  │
│  - user_id: 用户ID                                          │
│  - tenant_id: 租户ID                                        │
│  - resource: 资源类型 (student, class, etc.)                │
│  - action: 操作类型 (read, write, etc.)                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│          IdentityBasedDataFilter                            │
│  1. 查询用户身份信息                                         │
│  2. 检查通配符权限                                           │
│  3. 基于身份推导数据范围                                     │
│  4. 生成SQL过滤条件                                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                SQL过滤条件                                   │
│  - sql_condition: WHERE子句                                 │
│  - bind_params: 绑定参数                                     │
└─────────────────────────────────────────────────────────────┘
```

### 数据表关系

```sql
-- 用户身份表（租户schema内）
user_identities:
- id: 身份ID
- user_id: 用户ID
- role_id: 角色ID
- target_type: 目标类型 (school, grade, class, student)
- target_id: 目标ID (具体的年级ID、班级ID等)
- subject: 学科 (可选)

-- Casbin策略表（全局）
casbin_policies:
- ptype: 策略类型 (p, g, g2)
- v0: 主体 (角色名或用户标识)
- v1: 域 (租户ID)
- v2: 对象 (resource:* 通配符格式)
- v3: 动作 (read, write, etc.)
- v4: 效果 (allow, deny)
```

## 身份到数据范围的映射规则

### 1. 校长 (Principal)
- **身份特征**：`role_name = "principal"`, `target_type = "school"`
- **数据范围**：整个学校的所有数据
- **SQL条件**：`1=1` (无限制)

### 2. 年级长 (Grade Director)
- **身份特征**：`role_name = "grade_director"`, `target_type = "grade"`
- **数据范围**：所负责年级的数据
- **SQL条件**：`s.grade_id = ?` (绑定具体年级ID)

### 3. 班主任 (Class Teacher)
- **身份特征**：`role_name = "class_teacher"`, `target_type = "class"`
- **数据范围**：所负责班级的数据
- **SQL条件**：`s.administrative_class_id = ?` (绑定具体班级ID)

### 4. 任课老师 (Subject Teacher)
- **身份特征**：`role_name = "subject_teacher"`, `target_type = "class"`
- **数据范围**：所教授班级的数据
- **SQL条件**：通过教学关系表关联
```sql
s.administrative_class_id IN (
    SELECT class_id FROM teacher_class_subjects 
    WHERE teacher_user_id = ? AND subject = ?
)
```

### 5. 学科组长 (Subject Group Leader)
- **身份特征**：`role_name = "subject_group_leader"`, `target_type = "subject_group"`
- **数据范围**：学科组相关的数据
- **SQL条件**：通过学科组关联表
```sql
s.administrative_class_id IN (
    SELECT class_id FROM subject_group_classes 
    WHERE subject_group_id = ?
)
```

### 6. 学生 (Student)
- **身份特征**：`role_name = "student"`, `target_type = "student"`
- **数据范围**：只能访问自己的数据
- **SQL条件**：`s.id = ?` (绑定学生自己的ID)

### 7. 家长 (Parent)
- **身份特征**：`role_name = "parent"`, `target_type = "student"`
- **数据范围**：只能访问孩子的数据
- **SQL条件**：`s.id = ?` (绑定孩子的学生ID)

## 实现示例

### 1. 权限策略配置

```sql
-- 只需要配置通配符权限
INSERT INTO casbin_policies (ptype, v0, v1, v2, v3, v4) VALUES
('p', 'principal', 'tenant_001', 'student:*', 'read', 'allow'),
('p', 'grade_director', 'tenant_001', 'student:*', 'read', 'allow'),
('p', 'class_teacher', 'tenant_001', 'student:*', 'read', 'allow'),
('p', 'subject_teacher', 'tenant_001', 'student:*', 'read', 'allow'),
('p', 'student', 'tenant_001', 'student:*', 'read', 'allow'),
('p', 'parent', 'tenant_001', 'student:*', 'read', 'allow');
```

### 2. 用户身份数据

```sql
-- 用户身份示例
INSERT INTO tenant_001.user_identities VALUES
-- 校长身份
('uuid1', 'user_001', 'role_principal', 'school', NULL, NULL),
-- 年级长身份
('uuid2', 'user_002', 'role_grade_director', 'grade', 'grade_001', NULL),
-- 班主任身份
('uuid3', 'user_003', 'role_class_teacher', 'class', 'class_001', NULL),
-- 任课老师身份
('uuid4', 'user_004', 'role_subject_teacher', 'class', 'class_001', '数学'),
-- 学生身份
('uuid5', 'user_005', 'role_student', 'student', 'student_001', NULL),
-- 家长身份
('uuid6', 'user_006', 'role_parent', 'student', 'student_001', NULL);
```

### 3. 使用方式

```rust
// 在服务中使用
let filter_context = FilterContext {
    user_id: current_user_id,
    tenant_id: "tenant_001".to_string(),
    user_identity: format!("user:{}", current_user_id),
    resource: "student".to_string(),
    action: "read".to_string(),
    schema_name: "tenant_001".to_string(),
};

// 应用过滤器
data_filter_manager.apply_data_filter(
    &filter_context,
    &mut query_builder,
    &mut count_builder,
    casbin_service.as_ref(),
).await?;
```

## 优势

1. **简化权限配置**：casbin中只需要配置通配符权限，减少策略数量
2. **灵活的身份管理**：通过user_identities表灵活管理用户的多重身份
3. **动态范围计算**：根据身份信息动态计算数据访问范围
4. **易于扩展**：新增身份类型只需要在过滤器中添加匹配规则
5. **性能优化**：可以针对不同身份类型优化SQL查询

## 扩展性

### 添加新的身份类型

1. 在角色表中定义新角色
2. 在user_identities表中记录用户的新身份
3. 在IdentityBasedDataFilter中添加匹配规则：

```rust
match (identity.role_name.as_str(), identity.target_type.as_str(), resource) {
    // 新的身份类型
    ("new_role", "new_target_type", "student") => {
        // 生成对应的过滤条件
        conditions.push(FilterCondition {
            sql_condition: "custom_condition".to_string(),
            bind_params: vec![FilterParam::Uuid(target_id)],
        });
    },
    // ... 其他现有规则
}
```

### 支持复杂的数据关系

对于复杂的数据关系，可以通过子查询或JOIN来实现：

```rust
// 示例：教研组长可以访问多个学科的数据
("research_group_leader", "research_group", "student") => {
    let condition = format!(
        "s.administrative_class_id IN (
            SELECT DISTINCT tc.class_id 
            FROM teacher_class_subjects tc
            JOIN research_group_subjects rgs ON tc.subject = rgs.subject_name
            WHERE rgs.research_group_id = ?
        )"
    );
    conditions.push(FilterCondition {
        sql_condition: condition,
        bind_params: vec![FilterParam::Uuid(research_group_id)],
    });
}
```

## 最佳实践

1. **安全优先**：默认拒绝访问，只有明确匹配的身份才允许访问
2. **审计日志**：记录所有权限检查和数据访问的日志
3. **缓存优化**：缓存用户身份信息以提高性能
4. **测试覆盖**：为每种身份类型编写完整的测试用例
5. **监控告警**：监控权限检查失败的情况，及时发现配置问题
